package cn.jggroup.device.aspose;

import cn.jggroup.device.uitls.FactoryUtils;
import com.aspose.slides.IAutoShape;
import com.aspose.slides.IPPImage;
import com.aspose.slides.IParagraph;
import com.aspose.slides.IPictureFrame;
import com.aspose.slides.IRow;
import com.aspose.slides.IShape;
import com.aspose.slides.IShapeCollection;
import com.aspose.slides.ISlide;
import com.aspose.slides.ISlideCollection;
import com.aspose.slides.ITable;
import com.aspose.slides.ITextFrame;
import com.aspose.slides.License;
import com.aspose.slides.Presentation;
import com.aspose.slides.SaveFormat;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.imageio.ImageIO;
import org.apache.log4j.Logger;

/**
 * Aspose.Slides PowerPoint导出工具类
 *
 * 功能概述：
 * 基于Aspose.Slides组件生成电力系统周例会工作汇报PPT
 * 支持文本处理、表格生成、图片替换、样式渲染等功能
 *
 * 主要特性：
 * 1. 数据模拟 - 替代数据库查询，生成测试数据
 * 2. 智能分页 - 自动处理超长文本的分页显示
 * 3. 表格渲染 - 动态生成检修计划和风险作业表格
 * 4. 图片处理 - 自动查找和替换最新图片
 * 5. 关键词高亮 - 自动识别并高亮显示重要关键词
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-05-10
 */
public class ExportPptx {

	// ================================ 配置常量区域 ================================

	// 日志记录器
	private static final Logger log = Logger.getLogger(ExportPptx.class);

	// Aspose授权文件路径
	private static final String lic = getTemplatePathBy() + "license.xml";

	// PPT模板文件路径
	private static final String tplFile = getTemplatePathBy() + "pptx/module.pptx";

	// 图片文件存放路径
	private static final String imgFile = getTemplatePathBy() + "pptx/";

	// 图片文件名格式
	private static final String FILE_SUFFIX = ".png";

	// ================================ 数据配置映射区域 ================================

	// 处室表名映射：T1-T16对应各处室和功能的表名
	public static Map<String, String[]> tables = new HashMap<String, String[]>();

	// 填报项中文名映射：各处室对应的中文字段名
	public static Map<String, String[]> colsCn = new HashMap<String, String[]>();

	// 填报项英文名映射：各处室对应的英文字段名
	public static Map<String, String[]> colsEn = new HashMap<String, String[]>();

	// 特殊标记和序号配置：用于文本样式处理
	public static Map<String, String[]> colsXh = new HashMap<String, String[]>();

	// ================================ 静态初始化配置区域 ================================
	static {
		// 验证Aspose授权
		license();

		// -------------------- 处室和功能表名配置 --------------------
		// T1-T10：各处室数据表
		tables.put("T1", new String[] { "调控处", "MW_APP.NR_OMS_ZPTH_DKCTB" });
		tables.put("T2", new String[] { "计划处", "MW_APP.NR_OMS_ZPTH_JHCTB" });
		tables.put("T3", new String[] { "系统处", "MW_APP.NR_OMS_ZPTH_XTCTB" });
		tables.put("T4", new String[] { "保护处", "MW_APP.NR_OMS_ZPTH_BHCTB" });
		tables.put("T5", new String[] { "自动化处", "MW_APP.NR_OMS_ZPTH_ZDHCTB" });
		tables.put("T6", new String[] { "通信处", "MW_APP.NR_OMS_ZPTH_TXCTB" });
		tables.put("T7", new String[] { "网安处", "MW_APP.NR_OMS_ZPTH_WACTB" });
		tables.put("T8", new String[] { "现货处", "MW_APP.NR_OMS_ZPTH_XHCTB" });
		tables.put("T9", new String[] { "综合处", "MW_APP.NR_OMS_ZPTH_ZHCTB" });
		tables.put("T10", new String[] { "水新处", "MW_APP.NR_OMS_ZPTH_SXCTB" });

		// T11-T14：图表数据表
		tables.put("T11", new String[]{"本周检修计划安排1", "MW_APP.NR_OMS_ZPTH_JHCTB_T1"});
		tables.put("T12", new String[]{"检修计划开展情况2", "MW_APP.NR_OMS_ZPTH_JHCTB_T2"});
		tables.put("T13", new String[]{"检修计划开展情况3", "MW_APP.NR_OMS_ZPTH_JHCTB_T3"});
		tables.put("T14", new String[]{"二次作业风险表", "MW_APP.NR_OMS_ZPTH_GCTB_ZYFX"});

		// T15-T16：流程和排版配置表
		tables.put("T15", new String[]{"周例会流程", "MW_APP.MWT_UD_ZLHLC"});
		tables.put("T16", new String[]{"周例会排版", "MW_APP.MWT_UD_ZLHPB"});

		// -------------------- 各处室中文字段名配置 --------------------
		colsCn.put("T1", new String[] { "电网运行情况", "电网运行情况-重点工作", "上周山西电网设备停运情况统计", "上周省网最大负荷" });
		colsCn.put("T2", new String[] { "电网运行情况", "电力平衡方面", "本周检修计划安排", "月检修计划安排", "重点工作", "上周山西电网检修计划执行情况统计", "本周山西电网检修计划" });
		colsCn.put("T3", new String[] { "电网运行情况", "本周检修计划安排", "重点工作", "新设备安排情况统计" });
		colsCn.put("T4", new String[] { "二次作业风险", "重点工作" });
		colsCn.put("T5", new String[] { "二次作业风险", "重点工作" });
		colsCn.put("T6", new String[] { "二次作业风险", "重点工作" });
		colsCn.put("T7", new String[] { "二次作业风险", "重点工作" });
		colsCn.put("T8", new String[] { "重点工作" });
		colsCn.put("T9", new String[] { "重点工作" });
		colsCn.put("T10", new String[] { "重点工作" });

		// -------------------- 各处室英文字段名配置 --------------------
		colsEn.put("T1", new String[] { "dkc_dwyxqk", "dkc_dwyxqk_zdgz", "dkc_szsxdwsbtyqktj", "dkc_szswzdfh" });
		colsEn.put("T2", new String[] { "jhc_dwyxqk", "jhc_dlphfb", "jhc_bzjxjhap", "jhc_jxjhap", "jhc_zdgz", "jhc_szsxdwjxjhzxqktj", "jhc_bzsxdwjxjh" });
		colsEn.put("T3", new String[] { "xtc_dwyxqk", "xtc_bzjxjhap", "xtc_zdgz", "xtc_xsbapqktj" });
		colsEn.put("T4", new String[] { "bhc_eczyfx", "bhc_zdgz" });
		colsEn.put("T5", new String[] { "zdhc_eczyfx", "zdhc_zdgz" });
		colsEn.put("T6", new String[] { "txc_eczyfx", "txc_zdgz" });
		colsEn.put("T7", new String[] { "wac_eczyfx", "wac_zdgz" });
		colsEn.put("T8", new String[] { "xhc_zdgz" });
		colsEn.put("T9", new String[] { "zhc_zdgz" });
		colsEn.put("T10", new String[] { "sxc_zdgz" });

		// -------------------- 文本样式标记配置 --------------------
		// 特殊Unicode字符用于标记不同的文本样式类型
		colsXh.put("vf", new String[]{"龘", "骉", "魑", "魅", "魍", "魉"});
		// 需要特殊处理的文本类型（重点工作）
		colsXh.put("txt1", new String[]{"zdgz"});
		// 所有需要处理的字段键名列表
		colsXh.put("keys", new String[]{"dwyxqk1", "dwyxqk2", "dlphfb", "bzjxjhap1", "bzjxjhap2", "eczyfx", "jxjhap", "zdgz", "szsxdwsbtyqktj", "szsxdwjxjhzxqktj", "bzsxdwjxjh", "xsbapqktj", });
	}

	// ================================ 工具方法区域 ================================

	/**
	 * 获取模板文件路径
	 * @return 模板文件目录路径
	 */
	private static String getTemplatePathBy() {
		String templatePath = "src/main/resources/office/";
		return templatePath;
	}

	/**
	 * 获取导出文件路径
	 * @return 导出文件目录路径
	 */
	private static String getExportPathBy() {
		String exportPath = "src/test/resources/export/";
		return exportPath;
	}

	/**
	 * Aspose组件授权注册
	 * 用于激活Aspose.Slides组件的商业许可
	 */
	private static void license() {
		log.debug("Aspose授权文件：" + lic);
		License license = new License();
		try {
			license.setLicense(lic);
		} catch (Exception e) {
			log.warn("Aspose授权加载失败，将使用试用版功能：" + e.getMessage());
		}
	}

	/**
	 * 控制台信息输出
	 * @param msg 要输出的消息
	 */
	public static void print(String msg) {
		System.out.println(msg);
	}

	// ================================ 主程序入口区域 ================================

	/**
	 * 程序主入口
	 * 演示基于Aspose.Slides的PPT生成完整流程
	 * @param args 命令行参数（未使用）
	 */
	public static void main(String[] args) {
		Presentation pres = null;
		try {
			print("使用模板文件：" + tplFile);
			Date vDay = FactoryUtils.getDate2("2024-06-03");
			String name = getExportPathBy() + "ExportPptx" + ".pptx";
			pres = export(vDay);
			pres.save(name, SaveFormat.Pptx);
			print("导出完成：" + name);
		} catch (Exception e) {
			print("PPT导出失败：" + e.getMessage());
			e.printStackTrace();
		} finally {
			// 释放Presentation资源
			if (pres != null) {
				pres.dispose();
			}
		}
	}

	/******************************************************************************************************************/

	// 顺序必须为：二、处理表格  三、处理图片  一、处理文本
	public static Presentation export(Date date) throws IOException {
		String vDay = FactoryUtils.getDate(date, "yyyy-MM-dd");
		// 加载PPT
		Presentation pres = new Presentation(tplFile);
		ISlideCollection slds = pres.getSlides();

		// 关键字高亮显示
		String keyWords = "上周省网用电,公司外送,雁淮直流,超高压变电,重点生产工作,重点基建工作,建设分公司,输电,各地调,太原,大同,阳泉,长治,晋城,朔州,晋中,运城,忻州,临汾,吕梁,本周检修计划安排1表格中的电网风险等级和备注下填写的内容,二次作业风险表中的作业风险等级和备注下填写的内容";

		// 常量键值对
		Map<String, Object> kv = new HashMap<String, Object>();
		kv.put("vDay", FactoryUtils.getDate(FactoryUtils.getToday(), "yyyy年MM月dd日"));
		kv.put("vMonth", Integer.valueOf(FactoryUtils.getDate(date, "dd")) > 15 ? (Integer.valueOf(FactoryUtils.getDate(date, "MM")) + 1) + "月" : (Integer.valueOf(FactoryUtils.getDate(date, "MM"))) + "月");

		// -------------------- 二、处理表格数据 --------------------
		// T11.本周检修计划安排表（页索引为6）
		String[] colsEn = "xh,dw,jxsb,jxsj,dwdj,bz".split(",");
		String[] colsCn = "序号,单位,检修设备,检修时间,电网风险等级,备注".split(",");
		List<Map<String, Object>> t1 = queryMockTableData1(vDay);  // 使用模拟数据替代数据库查询
		t1 = formatList(t1, new String[]{"dw","jxsb","jxsj","dwdj","bz",});
		int addT1 = tableRender(t1, slds, colsEn, colsCn, 6,10);

		// T14.二次作业风险表（页索引为12）
		colsEn = "xh,dw,ztxmmc,zysj,zyfxdj,bz".split(",");
		colsCn = "序号,单位,作业（项目）名称,作业时间,作业风险等级,备注".split(",");
		List<Map<String, Object>> t2 = queryMockTableData2(vDay);  // 使用模拟数据替代数据库查询
		t2 = formatList(t2, new String[]{"dw", "ztxmmc", "zysj", "zyfxdj", "bz",});
		int addT2 = tableRender(t2, slds, colsEn, colsCn, 9 + addT1, 10);

		// 三、处理图片（页索引为2）
		File file = new File(imgFile + "module" + FILE_SUFFIX);
		if (file.exists()) {
			BufferedImage img = ImageIO.read(file);
			IPPImage image = pres.getImages().addImage(img);
			handerPic(slds.get_Item(2), image);
		}

		// -------------------- 一、处理文本数据 --------------------
		buildText(kv, vDay);      // 获取文本数据
		buildTextSplit(kv);       // 处理文本样式标记
		handerTxt(slds, kv, keyWords);  // 填充文本内容
		renderTxt(slds, keyWords);      // 渲染文本样式和分页

		return pres;
	}

	// ================================ 数据模拟区域 ================================

	/**
	 * 模拟查询文本数据
	 * 替代原有的数据库查询功能，生成测试用的文本内容
	 *
	 * @param result 结果映射对象
	 * @param rq 报告日期（格式：yyyy-MM-dd）
	 */
	public static void buildText(Map<String, Object> result, String rq) {
		// 使用模拟数据替代数据库查询
		Map<String, String> mockData = queryMockTextData(rq);

		String[] keys = colsXh.get("keys");
		for (int k = 0; k < keys.length; k++) {
			String rs = mockData.get(keys[k]);
			if (rs == null) {
				rs = "";  // 如果没有对应数据，设置为空字符串
			}
			result.put(keys[k], rs);
		}
	}

	/**
	 * 生成模拟文本数据
	 * @param rq 报告日期
	 * @return 包含各字段文本内容的映射
	 */
	private static Map<String, String> queryMockTextData(String rq) {
		Map<String, String> result = new HashMap<String, String>();

		// 电网运行情况相关数据
		result.put("dwyxqk1", "本周省网用电负荷平稳运行，最大负荷达到2850万千瓦，同比增长3.2%。电网运行总体稳定，未发生重大设备故障。");
		result.put("dwyxqk2", "电网运行方式合理，潮流分布均匀。系统频率、电压等指标均在正常范围内。");

		// 电力平衡方面
		result.put("dlphfb", "本周电力平衡总体良好，火电出力稳定，新能源发电量同比增长15%。预计下周用电负荷将有所上升。");

		// 检修计划安排
		result.put("bzjxjhap1", "本周安排500千伏检修计划3项，220千伏检修计划6项，110千伏检修计划12项。重点关注春季检修安全管控。");
		result.put("bzjxjhap2", "本周系统检修安排充分考虑电网安全约束，确保N-1安全准则。");
		result.put("jxjhap", "5月份检修计划安排：500千伏检修15项，220千伏检修28项，110千伏检修45项。确保迎峰度夏前完成重要设备检修。");

		// 二次作业风险
		result.put("eczyfx", "二次设备运行稳定，保护装置动作正确率100%。重点关注春季雷雨季节保护设备防护。自动化系统运行正常，数据采集完整率99.8%。");

		// 重点工作
		result.put("zdgz", "推进年度检修计划执行。加强检修现场安全管控。做好迎峰度夏检修准备工作。开展继电保护定值核查工作。推进调度自动化系统升级改造。");

		// 统计数据
		result.put("szsxdwsbtyqktj", "500千伏设备停运2项，220千伏设备停运5项，110千伏设备停运8项。主要原因为计划检修和设备维护。");
		result.put("szsxdwjxjhzxqktj", "上周完成检修计划执行率98.5%，其中500千伏完成率100%，220千伏完成率97.8%。");
		result.put("bzsxdwjxjh", "本周计划检修500千伏线路2条，220千伏线路4条，110千伏线路8条。");
		result.put("xsbapqktj", "新设备投运前安全评估3项，包括220千伏变电站扩建工程2项，110千伏新建工程1项。");

		return result;
	}

	/**
	 * 生成模拟表格数据1 - 本周检修计划安排表
	 * @param rq 报告日期
	 * @return 检修计划表格数据列表
	 */
	private static List<Map<String, Object>> queryMockTableData1(String rq) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();

		// 生成检修计划数据
		Map<String, Object> row1 = new HashMap<String, Object>();
		row1.put("xh", "1");
		row1.put("dw", "太原供电公司");
		row1.put("jxsb", "500千伏太原变1号主变");
		row1.put("jxsj", "2024-04-20 08:00-18:00");
		row1.put("dwdj", "黄色");
		row1.put("bz", "年度例行检修，电网风险等级：黄色");
		result.add(row1);

		Map<String, Object> row2 = new HashMap<String, Object>();
		row2.put("xh", "2");
		row2.put("dw", "大同供电公司");
		row2.put("jxsb", "220千伏大同东变2号主变");
		row2.put("jxsj", "2024-04-21 09:00-17:00");
		row2.put("dwdj", "绿色");
		row2.put("bz", "设备缺陷处理，电网风险等级：绿色");
		result.add(row2);

		Map<String, Object> row3 = new HashMap<String, Object>();
		row3.put("xh", "3");
		row3.put("dw", "阳泉供电公司");
		row3.put("jxsb", "110千伏阳泉变3号主变");
		row3.put("jxsj", "2024-04-22 08:30-16:30");
		row3.put("dwdj", "绿色");
		row3.put("bz", "预防性试验，电网风险等级：绿色");
		result.add(row3);

		Map<String, Object> row4 = new HashMap<String, Object>();
		row4.put("xh", "4");
		row4.put("dw", "长治供电公司");
		row4.put("jxsb", "220千伏长治南变1号主变");
		row4.put("jxsj", "2024-04-23 07:00-19:00");
		row4.put("dwdj", "橙色");
		row4.put("bz", "技术改造，电网风险等级：橙色");
		result.add(row4);

		Map<String, Object> row5 = new HashMap<String, Object>();
		row5.put("xh", "5");
		row5.put("dw", "晋城供电公司");
		row5.put("jxsb", "500千伏晋城变2号主变");
		row5.put("jxsj", "2024-04-24 08:00-18:00");
		row5.put("dwdj", "黄色");
		row5.put("bz", "年度检修，电网风险等级：黄色");
		result.add(row5);

		return result;
	}

	/**
	 * 生成模拟表格数据2 - 二次作业风险表
	 * @param rq 报告日期
	 * @return 二次作业风险表格数据列表
	 */
	private static List<Map<String, Object>> queryMockTableData2(String rq) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();

		// 生成二次作业风险数据
		Map<String, Object> risk1 = new HashMap<String, Object>();
		risk1.put("xh", "1");
		risk1.put("dw", "太原供电公司");
		risk1.put("ztxmmc", "500千伏太原变保护定值修改");
		risk1.put("zysj", "2024-04-20 10:00-12:00");
		risk1.put("zyfxdj", "中风险");
		risk1.put("bz", "配合主变检修，作业风险等级：中等，已制定详细安全措施");
		result.add(risk1);

		Map<String, Object> risk2 = new HashMap<String, Object>();
		risk2.put("xh", "2");
		risk2.put("dw", "大同供电公司");
		risk2.put("ztxmmc", "220千伏大同东变自动化调试");
		risk2.put("zysj", "2024-04-21 14:00-16:00");
		risk2.put("zyfxdj", "低风险");
		risk2.put("bz", "常规调试作业，作业风险等级：低等，按标准流程执行");
		result.add(risk2);

		Map<String, Object> risk3 = new HashMap<String, Object>();
		risk3.put("xh", "3");
		risk3.put("dw", "长治供电公司");
		risk3.put("ztxmmc", "220千伏长治南变通信设备更换");
		risk3.put("zysj", "2024-04-23 09:00-15:00");
		risk3.put("zyfxdj", "高风险");
		risk3.put("bz", "涉及重要通信链路，作业风险等级：高等，需专人监护");
		result.add(risk3);

		return result;
	}

	// ================================ 文本处理区域 ================================

	/**
	 * 文本样式标记处理方法
	 * 为不同类型的文本内容添加特殊的Unicode标记符号，用于后续样式渲染识别
	 * @param result 文本数据映射对象
	 */
	public static void buildTextSplit(Map<String, Object> result) {
		String[] vf = colsXh.get("vf");
		String[] txt1 = colsXh.get("txt1");
		String[] keys = colsXh.get("keys");
		for (int i = 0; i < keys.length; i++) {
			String key = keys[i];
			String value = (String) result.get(key);
			if (!"".equals(value) && value != null) {
				//
				value = value.trim();
				value = value.replaceAll("\n+", "\n").replaceAll("\r+", "\r");
				//
				String[] split = value.split("\r|\n");
				//
				boolean vFlag = false;
				for (int j = 0; j < txt1.length; j++) {
					if (txt1[j].equals(key)) {
						vFlag = true;
						break;
					}
				}
				if (vFlag) {
					for (int j = 0; j < split.length; j++) {
						if (j == 0) {
							value = vf[1] + split[0];
						} else {
							value = value + "\n" + split[j];
						}
					}
				} else {
					for (int j = 0; j < split.length; j++) {
						if (j == 0) {
							value = vf[0] + split[0];
						} else {
							value = value + "\n" + split[j];
						}
					}
				}
				result.put(key, value);
			} else {
				result.put(key, vf[0]);
			}
		}
	}

	// 填充文字内容，并将空白页删除
	public static void handerTxt(ISlideCollection slds, Map<String, Object> data, String keyWords) throws IOException {
		for (int i = 0; i < slds.size(); i++) {
			ISlide sld = slds.get_Item(i);
			for (IShape shape : sld.getShapes()) {
				if (shape instanceof IAutoShape) {
					IAutoShape ashp = (IAutoShape) shape;
					ITextFrame tf = ashp.getTextFrame();
					String txt = tf.getText();
					// 将txt渲染为html
					Set<String> keys = FactoryUtils.invokeRegxAll(txt, "\\{[a-z,A-Z,0-9_" + "]+\\}");
					for (String key : keys) {
						String vKey = key.replaceAll("[{}]", "");
						String vReg = key.replace("{", "\\{").replace("}", "\\}");
						txt = txt.replaceAll(vReg, convertTxt(data.get(vKey), keyWords));
					}
					// 将回车符进行div处理
					renderTxt(txt, tf);
				}
			}
		}
	}

	// 将txt渲染为html
	private static String convertTxt(Object src, String keyWords) {
		String txt = FactoryUtils.objToStr(src);
		String[] highLightKeys = keyWords.split(",");
		for (String keyWord : highLightKeys) {
			Set<String> keyWordSet = FactoryUtils.invokeRegxAll(txt,keyWord);
			for (String kw: keyWordSet ) {
				txt = txt.replaceAll(kw, "<strong style=\"color:#FC9804 !important; line-height: 120% !important;\">" + kw + "</strong>");
			}
		}
		return txt;
	}

	// 将回车符进行div处理
	private static void renderTxt(String txt, ITextFrame tf) {
		if (txt.startsWith("\r") || txt.startsWith("\n")) {
			txt = txt.replaceAll("^[\r|\n]+", "");
		}
		String[] text = txt.split("\\r|\\n");
		if (text.length > 1 || txt.contains("strong")) {
			tf.setText("");
			tf.getParagraphs().clear();
			String tmp = "";
			for (int j = 0; j < text.length; j++) {
				tmp = "<span style=\"margin:0;color:#FFFF00;font-size:32.0pt;font-family:'微软雅黑';line-height:120% !important;\">" + text[j] + "</span>";
				tf.getParagraphs().addFromHtml(tmp);
				IParagraph para = tf.getParagraphs().get_Item(j);
				para.getParagraphFormat().setIndent(64);
				para.getPortions().get_Item(0).getPortionFormat().getLineFormat().setWidth(12D);
			}
		} else {
			tf.setText(txt);
		}
	}

	// 如果超过一页的内容，则克隆当前页
	public static void renderTxt(ISlideCollection slds, String keyWords) {
		int index = 0;
		String txt = "";
		int lastIndex = 0;
		int vflag = -1;
		// 超过一页的页数
		outerLoop:
		for (int i = 0; i < slds.size(); i++) {
			ISlide sld = slds.get_Item(i);
			IShapeCollection shapes = sld.getShapes();
			IShape shape = shapes.get_Item(shapes.size() - 1);
			if (shape instanceof IAutoShape) {
				IAutoShape ashp = (IAutoShape) shape;
				ITextFrame tf = ashp.getTextFrame();
				txt = tf.getText();
				float height = ashp.getHeight();
				// 1行   51.823215
				// 2行   97.90322       46.080005
				// 5行   236.14326      46.081
				// 7行   328.30325
				// 8行   374.38324      46.08
				// 51.82 + 46.08*7 = 374.38
				if (height > 375) {
					index = i;
					vflag = isType(txt);
					lastIndex = getLastIndexAtHeight(txt, 375, vflag);
					break outerLoop;
				} else {
					// 未超过一页，如果存在特殊标识的文字，删除
					String[] vf = colsXh.get("vf");
					for (String str : vf) {
						if (txt.startsWith(str)) {
							txt = txt.substring(1);
							break;
						}
					}
					// 重写
					String convertTxt = convertTxt(txt, keyWords);
					renderTxt(convertTxt, tf);
				}
			}
		}
		// 1.存在，克隆当前页 2.循环PPT寻找下一个超过一页的页数
		if (index > 0) {
			// 字符串
			String text1 = "";
			String text2 = "";
			if (vflag > -1) {
				String bj = txt.substring(0, 1);
				text1 = txt.substring(1, lastIndex);
				text2 = bj + txt.substring(lastIndex);
			} else {
				text1 = txt.substring(0, lastIndex);
				text2 = txt.substring(lastIndex);
			}
			// 被克隆页
			ISlide sld1 = slds.get_Item(index);
			IShapeCollection shapes1 = sld1.getShapes();
			if (shapes1.size() > 0) {
				IShape shape1 = shapes1.get_Item(shapes1.size() - 1);
				if (shape1 instanceof IAutoShape) {
					IAutoShape ashp2 = (IAutoShape) shape1;
					ITextFrame tf1 = ashp2.getTextFrame();
					// 重写
					String convertTxt = convertTxt(text1, keyWords);
					renderTxt(convertTxt, tf1);
				}
			}
			// 克隆页
			slds.insertClone(index, slds.get_Item(index));
			ISlide sld2 = slds.get_Item(index+1);
			IShapeCollection shapes2 = sld2.getShapes();
			if (shapes2.size() > 0) {
				IShape shape2 = shapes2.get_Item(shapes2.size() - 1);
				if (shape2 instanceof IAutoShape) {
					IAutoShape ashp2 = (IAutoShape) shape2;
					ITextFrame tf2 = ashp2.getTextFrame();
					// 重写
					String convertTxt = convertTxt(text2, keyWords);
					renderTxt(convertTxt, tf2);
				}
			}
			// 循环PPT寻找下一个超过一页的页数
			renderTxt(slds, keyWords);
		}
	}

	// 判断样式类型
	public static int isType(String paragraphText) {
		String[] vf = colsXh.get("vf");
		int index = -1;
		for (int j = 0; j < vf.length; j++) {
			if (vf[j].equals(paragraphText.substring(0, 1))) {
				index = j;
				break;
			}
		}
		return index;
	}

	// 计算满一页时，最后一行最后一个字符索引位置
	public static int getLastIndexAtHeight(String txt, double targetHeight, int vflag) {
		// 计算总共有几行，返回每行的长度（因API中没有计算文本框中每行文字的长度，故此处粗略估算）
		int lineLength = 30;
		LinkedList<Integer> list = new LinkedList<Integer>();
		for (int index = 0; index < txt.length(); ) {
			int endIndex = Math.min(index + lineLength, txt.length());
			String line = txt.substring(index, endIndex);
			if (line.contains("\r")) {
				int breakIndex = line.indexOf("\r");
				String sub = line.substring(0, breakIndex + 1);
				list.add(sub.length() - 2);
				index += breakIndex + 1;
			} else {
				list.add(line.length() - 1);
				index += lineLength;
			}
		}

		// 计算满一页时，最后一行是第X行
		int num = 0;
		double currentHeight = 51.82;
		for (int i = 0; i < list.size(); i++) {
			if (currentHeight >= targetHeight) {
				num = i;
				break;
			}
			currentHeight += 46.08;
		}

		// 返回第X行最后一个字符，在txt索引的位置
		int index = 0;
		for (int i = 0; i < num; i++) {
			index += list.get(i);
		}

		// 根据不同标识，设置不同的分词规则
		if (vflag == 1) {
			// 找到当前第X行最后一个字符，这个字符前的最后一个句号
			// 从标志位后开始截取
			String sub = txt.substring(1, index);
			// 匹配是否以 数字+小数点 开头
			int pos = 0;
			// 匹配文本是否存在 数字+小数点
			boolean minFlag = false;
			int min = Integer.MAX_VALUE; //存最小值
			boolean maxFlag = false;
			int max = Integer.MIN_VALUE; //存最大值
			Set<String> sets2 = FactoryUtils.invokeRegxAll(sub, "\\d+\\.|\\d+\\．|\\d+\\、");
			for (String set : sets2) {
				int indexOf = sub.indexOf(set);
				if (indexOf >= 0) {
					if (indexOf < min) {
						min = indexOf;
						minFlag = true;
					}
					if (indexOf > max) {
						max = indexOf;
						maxFlag = true;
					}
				}
			}
			// 如果存在 数字+小数点，位于开头位置，则使用 最后匹配到。的索引
			boolean flag = false;
			if (minFlag || maxFlag) {
				if (minFlag && !maxFlag) {
					pos = sub.lastIndexOf("。");
					flag = false;
				} else if (!minFlag && maxFlag) {
					pos = sub.lastIndexOf("。");
					flag = false;
				} else if (minFlag && maxFlag) {
					if (min < max) {
						pos = max;
						flag = true;
					} else if (min == max && min != 0) {
						pos = max;
						flag = true;
					} else {
						pos = sub.lastIndexOf("。");
						flag = false;
					}
				}
			} else {
				pos = sub.lastIndexOf("。");
				flag = false;
			}
			// flag是否成立，取的索引位置不一样
			// 因为String sub = txt.substring(1, index);，故此处指针额外+1
			if (flag) {
				return pos + 1;
			} else {
				return pos + 2;
			}
		} else {
			// 找到当前第X行最后一个字符，这个字符前的最后一个句号
			String sub = txt.substring(0, index);
			int pos = sub.lastIndexOf("。") ;
			if (pos > -1) {
				return pos + 1;
			} else {
				return index;
			}
		}
	}

	// 代替JDK8的语法：String.join("\n", split);
	public static String join(String[] array, String delimiter) {
		if (array == null || array.length == 0) {
			return "";
		}
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < array.length; i++) {
			sb.append(array[i]);
			if (i < array.length - 1) {
				sb.append(delimiter);
			}
		}
		return sb.toString();
	}

	/******************************************************************************************************************/

	// 第二步：处理表格
	public static void handerTable(ISlide sld, List<Map<String, Object>> datas, String[] colsEn, String[] colsCn) {
		for (IShape shape : sld.getShapes()) {
			if (shape instanceof ITable) {
				ITable tab = (ITable) shape;
				int rowSize = tab.getRows().size();
				int colSize = tab.getColumns().size();
				for (int j = 0; j < colSize; j++) {
					tab.getRows().get_Item(0).get_Item(j).getTextFrame().setText(colsCn[j]);
				}
				for (int i = 1; i <= datas.size(); i++) {
					IRow row = null;
					if (i <= 1) {
						row = tab.getRows().get_Item(i);
					} else {
						row = tab.getRows().addClone(tab.getRows().get_Item(1), false)[0];
					}
					Map<String, Object> data = datas.get(i - 1);
					for (int j = 0; j < colSize; j++) {
						String val = FactoryUtils.objToStr(data.get(colsEn[j]));
						row.get_Item(j).getTextFrame().setText(val);
					}
				}
			}
		}
	}

	// 将表格大小进行分割（按长度为segmentSize X）
	public static LinkedList<List<Map<String, Object>>> tableSplit (List<Map<String, Object>> list, int segmentSize) {
		LinkedList<List<Map<String, Object>>> result = new LinkedList<List<Map<String, Object>>>();
		for (int i = 0; i < list.size(); i += segmentSize) {
			int end = Math.min(i + segmentSize, list.size());
			result.add(new ArrayList<Map<String, Object>>(list.subList(i, end)));
		}
		return result;
	}

	// 将表格进行页克隆
	public static int[] tableClone(ISlideCollection slds, int tableIndex, int listSize) {
		for (int i = 0; i < listSize - 1; i++) {
			slds.insertClone(tableIndex + i, slds.get_Item(tableIndex + i));
		}
		int[] counts = new int[listSize];
		for (int i = 0; i < listSize; i++) {
			counts[i] = tableIndex + i;
		}
		return counts;
	}

	// 将表格进行页渲染：tableIndex（表页码），segmentSize（分页大小）
	public static int tableRender(List<Map<String, Object>> t1, ISlideCollection slds, String[] colsEn, String[] colsCn, int tableIndex, int segmentSize) {
		int[] t1Counts = new int[1];
		if (!t1.isEmpty()) {
			LinkedList<List<Map<String, Object>>> t1List = tableSplit(t1, segmentSize);
			t1Counts = tableClone(slds, tableIndex, t1List.size());
			for (int i = 0; i < t1Counts.length; i++) {
				handerTable(slds.get_Item(t1Counts[i]), t1List.get(i), colsEn, colsCn);
			}
		} else {
			handerTable(slds.get_Item(tableIndex), t1, colsEn, colsCn);
		}
		return t1Counts.length - 1;
	}

	// 格式化数据
	public static List<Map<String, Object>> formatList(List<Map<String, Object>> result, String[] keys) {
		if (!result.isEmpty()) {
			int vc = 1;
			for (int i = 0; i < result.size(); i++) {
				Map<String, Object> map = result.get(i);
				int count = 0;
				for (String key : keys) {
					String obj = (String)map.get(key);
					if (obj == null || "".equals(obj.trim())) {
						count++;
					}
				}
				boolean vFlag = count == keys.length;
				if (vFlag) {
					result.remove(map);
				} else {
					map.put("xh", String.valueOf(vc++));
				}
			}
		}
		return result;
	}

	/******************************************************************************************************************/

	// 第三步：处理图片
	public static void handerPic(ISlide sld, IPPImage image) {
		for (IShape shape : sld.getShapes()) {
			if (shape instanceof IPictureFrame) {
				IPictureFrame pic = (IPictureFrame) shape;
				IPPImage img = pic.getPictureFormat().getPicture().getImage();
				img.replaceImage(image);
			}
		}
	}
}
