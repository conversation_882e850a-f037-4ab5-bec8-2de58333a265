//package cn.jggroup.device.aspose;
//
//import com.aspose.words.Document;
//import com.aspose.words.License;
//import com.aspose.words.SaveFormat;
//import java.awt.Color;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//public class ExportWordPb_bak {
//    private static final boolean dbFlag = true;
//    private static final String lic = getBasePathBy() + "license.xml";
//    private static final String tplFile = getBasePathBy() + "paiban.doc";
//    private static final String tplName = "周例会工作汇报_%1$s.docx";
//    private static final String imgFile = getBasePathBy();
//    private static final String imgName = "%1$s_%2$s.png";
//    private static final String imgRegex = "^%1$s_\\d+.png";
//    public static Map<String, String[]> tables = new HashMap<String, String[]>();
//    public static Map<String, String[]> colxhs = new HashMap<String, String[]>();
//    public static Map<String, String[]> highlights = new HashMap<String, String[]>();
//    public static Map<String, Map<String, Object>> drawRuns = new HashMap<String, Map<String, Object>>();
//
//    static {
//        // Initialize tables and columns as before...
//        // 处室
//        tables.put("T1", new String[]{"调控处", "MW_APP.NR_OMS_ZPTH_DKCTB"});
//        tables.put("T2", new String[]{"计划处", "MW_APP.NR_OMS_ZPTH_JHCTB"});
//        // ... (rest of initialization)
//
//        // 龘, 骉, 魑, 魍：分别对应4种样式
//        colxhs.put("vf", new String[]{"龘", "骉", "魑", "魅", "魍", "魉"});
//        colxhs.put("txt1", new String[]{"zdgz"});
//        colxhs.put("txt2", new String[]{"szsxdwjxjhzxqktj"});
//        colxhs.put("txt3", new String[]{"bzsxdwjxjh"});
//        colxhs.put("txt4", new String[]{"xsbapqktj"});
//        colxhs.put("txt2_split", new String[]{"检修.*\\d+项", "500千伏.*\\d+项", "220千伏.*\\d+项", "110千伏.*\\d+项", "35千伏.*\\d+项", "10千伏.*\\d+项",});
//        colxhs.put("txt3_split", new String[]{"检修.*\\d+项", "500千伏.*\\d+项", "220千伏.*\\d+项", "110千伏.*\\d+项", "35千伏.*\\d+项", "10千伏.*\\d+项",});
//        colxhs.put("txt4_split", new String[]{"启动.*\\d+项", "启.*\\d+项",});
//        colxhs.put("keys", new String[]{"dwyxqk1", "dwyxqk2", "dlphfb", "bzjxjhap1", "bzjxjhap2", "eczyfx", "jxjhap", "zdgz", "szsxdwsbtyqktj", "szsxdwjxjhzxqktj", "bzsxdwjxjh", "xsbapqktj",});
//
//        // 文字样式
//        drawRuns.put("s01", new HashMap<String, Object>() {{
//            put("bold", false);
//            put("size", 16.0D);
//            put("color", Color.BLACK);
//            put("name", "仿宋_GB2312");
//        }});
//        drawRuns.put("s02", new HashMap<String, Object>() {{
//            put("bold", true);
//            put("size", 16.0D);
//            put("color", Color.BLACK);
//            put("name", "仿宋_GB2312");
//        }});
//        // ... (rest of style initialization)
//    }
//
//    // Mock data method to replace MwUtils.queryList
//    private static List<Map<String, Object>> getMockData(String sql) {
//        List<Map<String, Object>> mockData = new ArrayList<>();
//
//        // Mock data for T11 (本周检修计划安排1)
//        if (sql.contains("NR_OMS_ZPTH_JHCTB_T1")) {
//            Map<String, Object> row1 = new HashMap<>();
//            row1.put("xh", "1");
//            row1.put("dw", "太原供电公司");
//            row1.put("jxsb", "110kV变电站主变检修");
//            row1.put("jxsj", "2024-06-03 08:00-17:00");
//            row1.put("dwdj", "低风险");
//            row1.put("bz", "例行检修");
//            mockData.add(row1);
//
//            Map<String, Object> row2 = new HashMap<>();
//            row2.put("xh", "2");
//            row2.put("dw", "长治供电公司");
//            row2.put("jxsb", "220kV输电线路检修");
//            row2.put("jxsj", "2024-06-04 09:00-16:00");
//            row2.put("dwdj", "中风险");
//            row2.put("bz", "紧急抢修");
//            mockData.add(row2);
//        }
//
//        // Mock data for T14 (二次作业风险表)
//        else if (sql.contains("NR_OMS_ZPTH_GCTB_ZYFX")) {
//            Map<String, Object> row1 = new HashMap<>();
//            row1.put("xh", "1");
//            row1.put("dw", "运城供电公司");
//            row1.put("ztxmmc", "继电保护装置检验");
//            row1.put("zysj", "2024-06-03 14:00-16:00");
//            row1.put("zyfxdj", "中风险");
//            row1.put("bz", "定期检验");
//            mockData.add(row1);
//        }
//
//        // Mock data for T16 (周例会排版)
//        else if (sql.contains("MWT_UD_ZLHPB")) {
//            Map<String, Object> row = new HashMap<>();
//            row.put("nr", "本周工作重点：完成年度检修计划的30%，确保电网安全稳定运行。");
//            mockData.add(row);
//        }
//
//        return mockData;
//    }
//
//    public static void main(String[] args) {
//        try {
//            print("模板：" + tplFile);
//            String rq = "2024-06-03";
//            Document doc = export(rq);
//            String path = getBasePathBy() + String.format(tplName, "20240603");
//            doc.save(path, SaveFormat.DOCX);
//            print("导出：" + path);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    public static Document export(String rq) {
//        // Implementation using mock data...
//        // Replace all mwUtils.queryList calls with getMockData
//        return null; // TODO: Implement actual export logic
//    }
//
//    private static String getBasePathBy() {
//        String basePath = "D:/logs/zlh/";
//        String os = System.getenv("os");
//        if (!("Windows_NT".equals(os))) {
//            basePath = "/home/<USER>/Portal/PROJECT-HOME/business/sxd5000/zhjs/zlh/out/";
//        }
//        return basePath;
//    }
//
//    private static void license() {
//        print("Aspose授权文件：" + lic);
//        License license = new License();
//        try {
//            license.setLicense(lic);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    public static void print(String msg) {
//        System.out.println(msg);
//    }
//}
