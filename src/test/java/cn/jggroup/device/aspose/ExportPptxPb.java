package cn.jggroup.device.aspose;//package com.ruoyi.exam.exam1;
//
//import com.aspose.slides.IAutoShape;
//import com.aspose.slides.IPPImage;
//import com.aspose.slides.IParagraph;
//import com.aspose.slides.IPictureFrame;
//import com.aspose.slides.IRow;
//import com.aspose.slides.IShape;
//import com.aspose.slides.IShapeCollection;
//import com.aspose.slides.ISlide;
//import com.aspose.slides.ISlideCollection;
//import com.aspose.slides.ITable;
//import com.aspose.slides.ITextFrame;
//import com.aspose.slides.License;
//import com.aspose.slides.Presentation;
//import com.aspose.slides.SaveFormat;
//import com.sxoms.utils.FactoryUtils;
//import com.sxoms.utils.MwUtils;
//import org.apache.log4j.Logger;
//
//import javax.imageio.ImageIO;
//import java.awt.image.BufferedImage;
//import java.io.File;
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
//public class ExportPptxPb {
//	//true.访问真实库 false.访问测试库
//	private static final Logger log = Logger.getLogger(ExportPptxPb.class);
//	private static final boolean dbFlag = true;
//	private static MwUtils mwUtils = new MwUtils(dbFlag);
//	private static final String lic = getBasePathBy() + "license.xml";
//	private static final String tplFile = getBasePathBy() + "paiban.pptx";
//	private static final String tplName = "周例会工作汇报_%1$s.pptx";
//	private static final String imgFile = getBasePathBy();
//	private static final String imgName = "%1$s_%2$s.png";
//	private static final String imgRegex = "^%1$s_\\d+.png";
//	public static Map<String, String[]> tables = new HashMap<String, String[]>();
//	public static Map<String, String[]> colsCn = new HashMap<String, String[]>();
//	public static Map<String, String[]> colsEn = new HashMap<String, String[]>();
//	public static Map<String, String[]> colsXh = new HashMap<String, String[]>();
//
//	static {
//		// 验证授权
//		license();
//
//		// 处室
//		tables.put("T1", new String[] { "调控处", "MW_APP.NR_OMS_ZPTH_DKCTB" });
//		tables.put("T2", new String[] { "计划处", "MW_APP.NR_OMS_ZPTH_JHCTB" });
//		tables.put("T3", new String[] { "系统处", "MW_APP.NR_OMS_ZPTH_XTCTB" });
//		tables.put("T4", new String[] { "保护处", "MW_APP.NR_OMS_ZPTH_BHCTB" });
//		tables.put("T5", new String[] { "自动化处", "MW_APP.NR_OMS_ZPTH_ZDHCTB" });
//		tables.put("T6", new String[] { "通信处", "MW_APP.NR_OMS_ZPTH_TXCTB" });
//		tables.put("T7", new String[] { "网安处", "MW_APP.NR_OMS_ZPTH_WACTB" });
//		tables.put("T8", new String[] { "现货处", "MW_APP.NR_OMS_ZPTH_XHCTB" });
//		tables.put("T9", new String[] { "综合处", "MW_APP.NR_OMS_ZPTH_ZHCTB" });
//		tables.put("T10", new String[] { "水新处", "MW_APP.NR_OMS_ZPTH_SXCTB" });
//		// 图表
//		tables.put("T11", new String[]{"本周检修计划安排1", "MW_APP.NR_OMS_ZPTH_JHCTB_T1"});
//		tables.put("T12", new String[]{"检修计划开展情况2", "MW_APP.NR_OMS_ZPTH_JHCTB_T2"});
//		tables.put("T13", new String[]{"检修计划开展情况3", "MW_APP.NR_OMS_ZPTH_JHCTB_T3"});
//		tables.put("T14", new String[]{"二次作业风险表", "MW_APP.NR_OMS_ZPTH_GCTB_ZYFX"});
//		// 流程
//		tables.put("T15", new String[]{"周例会流程", "MW_APP.MWT_UD_ZLHLC"});
//		// 排版
//		tables.put("T16", new String[]{"周例会排版", "MW_APP.MWT_UD_ZLHPB"});
//
//		// 填报项-中文名
//		colsCn.put("T1", new String[] { "电网运行情况", "电网运行情况-重点工作", "上周山西电网设备停运情况统计", "上周省网最大负荷" });
//		colsCn.put("T2", new String[] { "电网运行情况", "电力平衡方面", "本周检修计划安排", "月检修计划安排", "重点工作", "上周山西电网检修计划执行情况统计", "本周山西电网检修计划" });
//		colsCn.put("T3", new String[] { "电网运行情况", "本周检修计划安排", "重点工作", "新设备安排情况统计" });
//		colsCn.put("T4", new String[] { "二次作业风险", "重点工作" });
//		colsCn.put("T5", new String[] { "二次作业风险", "重点工作" });
//		colsCn.put("T6", new String[] { "二次作业风险", "重点工作" });
//		colsCn.put("T7", new String[] { "二次作业风险", "重点工作" });
//		colsCn.put("T8", new String[] { "重点工作" });
//		colsCn.put("T9", new String[] { "重点工作" });
//		colsCn.put("T10", new String[] { "重点工作" });
//
//		// 填报项-英文名
//		colsEn.put("T1", new String[] { "dkc_dwyxqk", "dkc_dwyxqk_zdgz", "dkc_szsxdwsbtyqktj", "dkc_szswzdfh" });
//		colsEn.put("T2", new String[] { "jhc_dwyxqk", "jhc_dlphfb", "jhc_bzjxjhap", "jhc_jxjhap", "jhc_zdgz", "jhc_szsxdwjxjhzxqktj", "jhc_bzsxdwjxjh" });
//		colsEn.put("T3", new String[] { "xtc_dwyxqk", "xtc_bzjxjhap", "xtc_zdgz", "xtc_xsbapqktj" });
//		colsEn.put("T4", new String[] { "bhc_eczyfx", "bhc_zdgz" });
//		colsEn.put("T5", new String[] { "zdhc_eczyfx", "zdhc_zdgz" });
//		colsEn.put("T6", new String[] { "txc_eczyfx", "txc_zdgz" });
//		colsEn.put("T7", new String[] { "wac_eczyfx", "wac_zdgz" });
//		colsEn.put("T8", new String[] { "xhc_zdgz" });
//		colsEn.put("T9", new String[] { "zhc_zdgz" });
//		colsEn.put("T10", new String[] { "sxc_zdgz" });
//
//		// 段落+序号
//		colsXh.put("vf", new String[]{"龘", "骉", "魑", "魅", "魍", "魉"});
//		colsXh.put("txt1", new String[]{"zdgz"});
//		colsXh.put("keys", new String[]{"dwyxqk1", "dwyxqk2", "dlphfb", "bzjxjhap1", "bzjxjhap2", "eczyfx", "jxjhap", "zdgz", "szsxdwsbtyqktj", "szsxdwjxjhzxqktj", "bzsxdwjxjh", "xsbapqktj", });
//	}
//
//	/**
//	 * 路径
//	 */
//	private static String getBasePathBy() {
//		String basePath = "D:/logs/zlh/";
//		String os = System.getenv("os");
//		if (!("Windows_NT".equals(os))) {
//			basePath = "/home/<USER>/Portal/PROJECT-HOME/business/sxd5000/zhjs/zlh/out/";
//		}
//		return basePath;
//	}
//
//	/**
//	 * Aspose组件注册
//	 */
//	private static void license() {
//		log.debug("Aspose授权文件：" + lic);
//		License license = new License();
//		try {
//			license.setLicense(lic);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
//
//	/**
//	 * 打印消息
//	 */
//	public static void print(String msg) {
//		System.out.println(msg);
//	}
//
//	/******************************************************************************************************************/
//
//	public static void main(String[] args) {
//		Presentation pres = null;
//		try {
//			print("模板文件：" + tplFile);
//			Date vDay = FactoryUtils.getDate2("2024-06-03");
//			String name = getBasePathBy() + String.format(tplName, FactoryUtils.getDate(FactoryUtils.getToday(), "yyyyMMdd"));
//			pres = export(vDay);
//			pres.save(name, SaveFormat.Pptx);
//			print("导出：" + name);
//		} catch (Exception e) {
//			e.printStackTrace();
//		} finally {
//			if (pres != null) {
//				pres.dispose();
//			}
//		}
//	}
//
//	/******************************************************************************************************************/
//
//	// 顺序必须为：二、处理表格  三、处理图片  一、处理文本
//	public static Presentation export(Date date) throws IOException {
//		String vDay = FactoryUtils.getDate(date, "yyyy-MM-dd");
//		// 加载PPT
//		Presentation pres = new Presentation(tplFile);
//		ISlideCollection slds = pres.getSlides();
//
//		// 关键字高亮显示
//		String keyWords = "上周省网用电,公司外送,雁淮直流,超高压变电,重点生产工作,重点基建工作,建设分公司,输电,各地调,太原,大同,阳泉,长治,晋城,朔州,晋中,运城,忻州,临汾,吕梁,本周检修计划安排1表格中的电网风险等级和备注下填写的内容,二次作业风险表中的作业风险等级和备注下填写的内容";
//
//		// 常量键值对
//		Map<String, Object> kv = new HashMap<String, Object>();
//		kv.put("vDay", FactoryUtils.getDate(FactoryUtils.getToday(), "yyyy年MM月dd日"));
//		kv.put("vMonth", Integer.valueOf(FactoryUtils.getDate(date, "dd")) > 15 ? (Integer.valueOf(FactoryUtils.getDate(date, "MM")) + 1) + "月" : (Integer.valueOf(FactoryUtils.getDate(date, "MM"))) + "月");
//
//		// 二、处理表格
//		// T11.本周检修计划安排（页索引为6）
//		String[] colsEn = "xh,dw,jxsb,jxsj,dwdj,bz".split(",");
//		String[] colsCn = "序号,单位,检修设备,检修时间,电网风险等级,备注".split(",");
//		String sql = "SELECT xh, DECODE(dw, null, ' ', 'null', ' ', dw) AS dw, DECODE(jxsb, null, ' ', 'null', ' ', jxsb) AS jxsb, DECODE(jxsj, null, ' ', 'null', ' ', jxsj) AS jxsj, DECODE(dwdj, null, ' ', 'null', ' ', dwdj) AS dwdj, DECODE(bz, null, ' ', 'null', ' ', bz) AS bz FROM MW_APP.NR_OMS_ZPTH_JHCTB_T1 WHERE RQ = '" + vDay + "' ORDER BY CAST(XH AS NUMBER)";
//		List<Map<String, Object>> t1 = mwUtils.queryList(sql);
//		t1 = formatList(t1, new String[]{"dw","jxsb","jxsj","dwdj","bz",});
//		int addT1 = tableRender(t1, slds, colsEn, colsCn, 6,10);
//
//		// T14.二次作业风险表（页索引为12）
//		colsEn = "xh,dw,ztxmmc,zysj,zyfxdj,bz".split(",");
//		colsCn = "序号,单位,作业（项目）名称,作业时间,作业风险等级,备注".split(",");
//		sql = "SELECT xh, DECODE(dw, null, ' ', 'null', ' ', dw) AS dw, DECODE(ztxmmc, null, ' ', 'null', ' ', ztxmmc) AS ztxmmc, DECODE(zysj, null, ' ', 'null', ' ', zysj) AS zysj, DECODE(zyfxdj, null, ' ', 'null', ' ', zyfxdj) AS zyfxdj, DECODE(bz, null, ' ', 'null', ' ', bz) AS bz FROM MW_APP.NR_OMS_ZPTH_GCTB_ZYFX WHERE RQ = '" + vDay + "' ORDER BY CS";
//		List<Map<String, Object>> t2 = mwUtils.queryList(sql);
//		t2 = formatList(t2, new String[]{"dw", "ztxmmc", "zysj", "zyfxdj", "bz",});
//		int addT2 = tableRender(t2, slds, colsEn, colsCn, 9 + addT1, 10);
//
//		// 三、处理图片（页索引为2）
//		int xh = queryImgMax(FactoryUtils.getDate(date));
//		if (xh > -1) {
//			File file = new File(imgFile + String.format(imgName, FactoryUtils.getDate(date, "yyyyMMdd"), xh));
//			if (file.exists()) {
//				BufferedImage img = ImageIO.read(file);
//				IPPImage image = pres.getImages().addImage(img);
//				handerPic(slds.get_Item(2), image);
//			}
//		} else {
//			pres.getSlides().removeAt(2);
//		}
//
//		// 一、处理文本
//		buildText(kv, vDay);
//		buildTextSplit(kv);
//		handerTxt(slds, kv, keyWords);
//		renderTxt(slds, keyWords);
//
//		return pres;
//	}
//
//	/******************************************************************************************************************/
//
//	// 第一步：处理文本
//	public static void buildText(Map<String, Object> result, String rq) {
//		String[] keys = colsXh.get("keys");
//		for (int k = 0; k < keys.length; k++) {
//			String sql = "SELECT NR FROM " + tables.get("T16")[1] + " WHERE LX = '" + keys[k] + "' AND RQ = '" + rq + "'";
//			// print("SQL：" + sql);
//			List<Map<String, Object>> ls = mwUtils.queryList(sql);
//			String rs = "";
//			if (ls.size() > 0) {
//				rs = (String) ls.get(0).get("nr");
//			}
//			result.put(keys[k], rs);
//		}
//	}
//
//	// 处理文字前缀，用于标记处理的特殊段落
//	public static void buildTextSplit(Map<String, Object> result) {
//		String[] vf = colsXh.get("vf");
//		String[] txt1 = colsXh.get("txt1");
//		String[] keys = colsXh.get("keys");
//		for (int i = 0; i < keys.length; i++) {
//			String key = keys[i];
//			String value = (String) result.get(key);
//			if (!"".equals(value) && value != null) {
//				//
//				value = value.trim();
//				value = value.replaceAll("\n+", "\n").replaceAll("\r+", "\r");
//				//
//				String[] split = value.split("\r|\n");
//				//
//				boolean vFlag = false;
//				for (int j = 0; j < txt1.length; j++) {
//					if (txt1[j].equals(key)) {
//						vFlag = true;
//						break;
//					}
//				}
//				if (vFlag) {
//					for (int j = 0; j < split.length; j++) {
//						if (j == 0) {
//							value = vf[1] + split[0];
//						} else {
//							value = value + "\n" + split[j];
//						}
//					}
//				} else {
//					for (int j = 0; j < split.length; j++) {
//						if (j == 0) {
//							value = vf[0] + split[0];
//						} else {
//							value = value + "\n" + split[j];
//						}
//					}
//				}
//				result.put(key, value);
//			} else {
//				result.put(key, vf[0]);
//			}
//		}
//	}
//
//	// 填充文字内容，并将空白页删除
//	public static void handerTxt(ISlideCollection slds, Map<String, Object> data, String keyWords) throws IOException {
//		for (int i = 0; i < slds.size(); i++) {
//			ISlide sld = slds.get_Item(i);
//			for (IShape shape : sld.getShapes()) {
//				if (shape instanceof IAutoShape) {
//					IAutoShape ashp = (IAutoShape) shape;
//					ITextFrame tf = ashp.getTextFrame();
//					String txt = tf.getText();
//					// 将txt渲染为html
//					Set<String> keys = FactoryUtils.invokeRegxAll(txt, "\\{[a-z,A-Z,0-9_" + "]+\\}");
//					for (String key : keys) {
//						String vKey = key.replaceAll("[{}]", "");
//						String vReg = key.replace("{", "\\{").replace("}", "\\}");
//						txt = txt.replaceAll(vReg, convertTxt(data.get(vKey), keyWords));
//					}
//					// 将回车符进行div处理
//					renderTxt(txt, tf);
//				}
//			}
//		}
//	}
//
//	// 将txt渲染为html
//	private static String convertTxt(Object src, String keyWords) {
//		String txt = FactoryUtils.objToStr(src);
//		String[] highLightKeys = keyWords.split(",");
//		for (String keyWord : highLightKeys) {
//			Set<String> keyWordSet = FactoryUtils.invokeRegxAll(txt,keyWord);
//			for (String kw: keyWordSet ) {
//				txt = txt.replaceAll(kw, "<strong style=\"color:#FC9804 !important; line-height: 120% !important;\">" + kw + "</strong>");
//			}
//		}
//		return txt;
//	}
//
//	// 将回车符进行div处理
//	private static void renderTxt(String txt, ITextFrame tf) {
//		if (txt.startsWith("\r") || txt.startsWith("\n")) {
//			txt = txt.replaceAll("^[\r|\n]+", "");
//		}
//		String[] text = txt.split("\\r|\\n");
//		if (text.length > 1 || txt.contains("strong")) {
//			tf.setText("");
//			tf.getParagraphs().clear();
//			String tmp = "";
//			for (int j = 0; j < text.length; j++) {
//				tmp = "<span style=\"margin:0;color:#FFFF00;font-size:32.0pt;font-family:'微软雅黑';line-height:120% !important;\">" + text[j] + "</span>";
//				tf.getParagraphs().addFromHtml(tmp);
//				IParagraph para = tf.getParagraphs().get_Item(j);
//				para.getParagraphFormat().setIndent(64);
//				para.getPortions().get_Item(0).getPortionFormat().getLineFormat().setWidth(12D);
//			}
//		} else {
//			tf.setText(txt);
//		}
//	}
//
//	// 如果超过一页的内容，则克隆当前页
//	public static void renderTxt(ISlideCollection slds, String keyWords) {
//		int index = 0;
//		String txt = "";
//		int lastIndex = 0;
//		int vflag = -1;
//		// 超过一页的页数
//		outerLoop:
//		for (int i = 0; i < slds.size(); i++) {
//			ISlide sld = slds.get_Item(i);
//			IShapeCollection shapes = sld.getShapes();
//			IShape shape = shapes.get_Item(shapes.size() - 1);
//			if (shape instanceof IAutoShape) {
//				IAutoShape ashp = (IAutoShape) shape;
//				ITextFrame tf = ashp.getTextFrame();
//				txt = tf.getText();
//				float height = ashp.getHeight();
//				// 1行   51.823215
//				// 2行   97.90322       46.080005
//				// 5行   236.14326      46.081
//				// 7行   328.30325
//				// 8行   374.38324      46.08
//				// 51.82 + 46.08*7 = 374.38
//				if (height > 375) {
//					index = i;
//					vflag = isType(txt);
//					lastIndex = getLastIndexAtHeight(txt, 375, vflag);
//					break outerLoop;
//				} else {
//					// 未超过一页，如果存在特殊标识的文字，删除
//					String[] vf = colsXh.get("vf");
//					for (String str : vf) {
//						if (txt.startsWith(str)) {
//							txt = txt.substring(1);
//							break;
//						}
//					}
//					// 重写
//					String convertTxt = convertTxt(txt, keyWords);
//					renderTxt(convertTxt, tf);
//				}
//			}
//		}
//		// 1.存在，克隆当前页 2.循环PPT寻找下一个超过一页的页数
//		if (index > 0) {
//			// 字符串
//			String text1 = "";
//			String text2 = "";
//			if (vflag > -1) {
//				String bj = txt.substring(0, 1);
//				text1 = txt.substring(1, lastIndex);
//				text2 = bj + txt.substring(lastIndex);
//			} else {
//				text1 = txt.substring(0, lastIndex);
//				text2 = txt.substring(lastIndex);
//			}
//			// 被克隆页
//			ISlide sld1 = slds.get_Item(index);
//			IShapeCollection shapes1 = sld1.getShapes();
//			if (shapes1.size() > 0) {
//				IShape shape1 = shapes1.get_Item(shapes1.size() - 1);
//				if (shape1 instanceof IAutoShape) {
//					IAutoShape ashp2 = (IAutoShape) shape1;
//					ITextFrame tf1 = ashp2.getTextFrame();
//					// 重写
//					String convertTxt = convertTxt(text1, keyWords);
//					renderTxt(convertTxt, tf1);
//				}
//			}
//			// 克隆页
//			slds.insertClone(index, slds.get_Item(index));
//			ISlide sld2 = slds.get_Item(index+1);
//			IShapeCollection shapes2 = sld2.getShapes();
//			if (shapes2.size() > 0) {
//				IShape shape2 = shapes2.get_Item(shapes2.size() - 1);
//				if (shape2 instanceof IAutoShape) {
//					IAutoShape ashp2 = (IAutoShape) shape2;
//					ITextFrame tf2 = ashp2.getTextFrame();
//					// 重写
//					String convertTxt = convertTxt(text2, keyWords);
//					renderTxt(convertTxt, tf2);
//				}
//			}
//			// 循环PPT寻找下一个超过一页的页数
//			renderTxt(slds, keyWords);
//		}
//	}
//
//	// 判断样式类型
//	public static int isType(String paragraphText) {
//		String[] vf = colsXh.get("vf");
//		int index = -1;
//		for (int j = 0; j < vf.length; j++) {
//			if (vf[j].equals(paragraphText.substring(0, 1))) {
//				index = j;
//				break;
//			}
//		}
//		return index;
//	}
//
//	// 计算满一页时，最后一行最后一个字符索引位置
//	public static int getLastIndexAtHeight(String txt, double targetHeight, int vflag) {
//		// 计算总共有几行，返回每行的长度（因API中没有计算文本框中每行文字的长度，故此处粗略估算）
//		int lineLength = 30;
//		LinkedList<Integer> list = new LinkedList<Integer>();
//		for (int index = 0; index < txt.length(); ) {
//			int endIndex = Math.min(index + lineLength, txt.length());
//			String line = txt.substring(index, endIndex);
//			if (line.contains("\r")) {
//				int breakIndex = line.indexOf("\r");
//				String sub = line.substring(0, breakIndex + 1);
//				list.add(sub.length() - 2);
//				index += breakIndex + 1;
//			} else {
//				list.add(line.length() - 1);
//				index += lineLength;
//			}
//		}
//
//		// 计算满一页时，最后一行是第X行
//		int num = 0;
//		double currentHeight = 51.82;
//		for (int i = 0; i < list.size(); i++) {
//			if (currentHeight >= targetHeight) {
//				num = i;
//				break;
//			}
//			currentHeight += 46.08;
//		}
//
//		// 返回第X行最后一个字符，在txt索引的位置
//		int index = 0;
//		for (int i = 0; i < num; i++) {
//			index += list.get(i);
//		}
//
//		// 根据不同标识，设置不同的分词规则
//		if (vflag == 1) {
//			// 找到当前第X行最后一个字符，这个字符前的最后一个句号
//			// 从标志位后开始截取
//			String sub = txt.substring(1, index);
//			// 匹配是否以 数字+小数点 开头
//			int pos = 0;
//			// 匹配文本是否存在 数字+小数点
//			boolean minFlag = false;
//			int min = Integer.MAX_VALUE; //存最小值
//			boolean maxFlag = false;
//			int max = Integer.MIN_VALUE; //存最大值
//			Set<String> sets2 = FactoryUtils.invokeRegxAll(sub, "\\d+\\.|\\d+\\．|\\d+\\、");
//			for (String set : sets2) {
//				int indexOf = sub.indexOf(set);
//				if (indexOf >= 0) {
//					if (indexOf < min) {
//						min = indexOf;
//						minFlag = true;
//					}
//					if (indexOf > max) {
//						max = indexOf;
//						maxFlag = true;
//					}
//				}
//			}
//			// 如果存在 数字+小数点，位于开头位置，则使用 最后匹配到。的索引
//			boolean flag = false;
//			if (minFlag || maxFlag) {
//				if (minFlag && !maxFlag) {
//					pos = sub.lastIndexOf("。");
//					flag = false;
//				} else if (!minFlag && maxFlag) {
//					pos = sub.lastIndexOf("。");
//					flag = false;
//				} else if (minFlag && maxFlag) {
//					if (min < max) {
//						pos = max;
//						flag = true;
//					} else if (min == max && min != 0) {
//						pos = max;
//						flag = true;
//					} else {
//						pos = sub.lastIndexOf("。");
//						flag = false;
//					}
//				}
//			} else {
//				pos = sub.lastIndexOf("。");
//				flag = false;
//			}
//			// flag是否成立，取的索引位置不一样
//			// 因为String sub = txt.substring(1, index);，故此处指针额外+1
//			if (flag) {
//				return pos + 1;
//			} else {
//				return pos + 2;
//			}
//		} else {
//			// 找到当前第X行最后一个字符，这个字符前的最后一个句号
//			String sub = txt.substring(0, index);
//			int pos = sub.lastIndexOf("。") ;
//			if (pos > -1) {
//				return pos + 1;
//			} else {
//				return index;
//			}
//		}
//	}
//
//	// 代替JDK8的语法：String.join("\n", split);
//	public static String join(String[] array, String delimiter) {
//		if (array == null || array.length == 0) {
//			return "";
//		}
//		StringBuilder sb = new StringBuilder();
//		for (int i = 0; i < array.length; i++) {
//			sb.append(array[i]);
//			if (i < array.length - 1) {
//				sb.append(delimiter);
//			}
//		}
//		return sb.toString();
//	}
//
//	/******************************************************************************************************************/
//
//	// 第二步：处理表格
//	public static void handerTable(ISlide sld, List<Map<String, Object>> datas, String[] colsEn, String[] colsCn) {
//		for (IShape shape : sld.getShapes()) {
//			if (shape instanceof ITable) {
//				ITable tab = (ITable) shape;
//				int rowSize = tab.getRows().size();
//				int colSize = tab.getColumns().size();
//				for (int j = 0; j < colSize; j++) {
//					tab.getRows().get_Item(0).get_Item(j).getTextFrame().setText(colsCn[j]);
//				}
//				for (int i = 1; i <= datas.size(); i++) {
//					IRow row = null;
//					if (i <= 1) {
//						row = tab.getRows().get_Item(i);
//					} else {
//						row = tab.getRows().addClone(tab.getRows().get_Item(1), false)[0];
//					}
//					Map<String, Object> data = datas.get(i - 1);
//					for (int j = 0; j < colSize; j++) {
//						String val = FactoryUtils.objToStr(data.get(colsEn[j]));
//						row.get_Item(j).getTextFrame().setText(val);
//					}
//				}
//			}
//		}
//	}
//
//	// 将表格大小进行分割（按长度为segmentSize X）
//	public static LinkedList<List<Map<String, Object>>> tableSplit (List<Map<String, Object>> list, int segmentSize) {
//		LinkedList<List<Map<String, Object>>> result = new LinkedList<List<Map<String, Object>>>();
//		for (int i = 0; i < list.size(); i += segmentSize) {
//			int end = Math.min(i + segmentSize, list.size());
//			result.add(new ArrayList<Map<String, Object>>(list.subList(i, end)));
//		}
//		return result;
//	}
//
//	// 将表格进行页克隆
//	public static int[] tableClone(ISlideCollection slds, int tableIndex, int listSize) {
//		for (int i = 0; i < listSize - 1; i++) {
//			slds.insertClone(tableIndex + i, slds.get_Item(tableIndex + i));
//		}
//		int[] counts = new int[listSize];
//		for (int i = 0; i < listSize; i++) {
//			counts[i] = tableIndex + i;
//		}
//		return counts;
//	}
//
//	// 将表格进行页渲染：tableIndex（表页码），segmentSize（分页大小）
//	public static int tableRender(List<Map<String, Object>> t1, ISlideCollection slds, String[] colsEn, String[] colsCn, int tableIndex, int segmentSize) {
//		int[] t1Counts = new int[1];
//		if (!t1.isEmpty()) {
//			LinkedList<List<Map<String, Object>>> t1List = tableSplit(t1, segmentSize);
//			t1Counts = tableClone(slds, tableIndex, t1List.size());
//			for (int i = 0; i < t1Counts.length; i++) {
//				handerTable(slds.get_Item(t1Counts[i]), t1List.get(i), colsEn, colsCn);
//			}
//		} else {
//			handerTable(slds.get_Item(tableIndex), t1, colsEn, colsCn);
//		}
//		return t1Counts.length - 1;
//	}
//
//	// 格式化数据
//	public static List<Map<String, Object>> formatList(List<Map<String, Object>> result, String[] keys) {
//		if (!result.isEmpty()) {
//			int vc = 1;
//			for (int i = 0; i < result.size(); i++) {
//				Map<String, Object> map = result.get(i);
//				int count = 0;
//				for (String key : keys) {
//					String obj = (String)map.get(key);
//					if (obj == null || "".equals(obj.trim())) {
//						count++;
//					}
//				}
//				boolean vFlag = count == keys.length;
//				if (vFlag) {
//					result.remove(map);
//				} else {
//					map.put("xh", String.valueOf(vc++));
//				}
//			}
//		}
//		return result;
//	}
//
//	/******************************************************************************************************************/
//
//	// 第三步：处理图片
//	public static void handerPic(ISlide sld, IPPImage image) {
//		for (IShape shape : sld.getShapes()) {
//			if (shape instanceof IPictureFrame) {
//				IPictureFrame pic = (IPictureFrame) shape;
//				IPPImage img = pic.getPictureFormat().getPicture().getImage();
//				img.replaceImage(image);
//			}
//		}
//	}
//
//	// 查询图片最大序号
//	public static int queryImgMax(String rq) {
//		// 文件夹路径
//		File folder = new File(imgFile);
//
//		// 正则表达式
//		String regex = String.format(imgRegex, FactoryUtils.getDate(rq, "yyyyMMdd"));
//		Pattern pattern = Pattern.compile(regex);
//
//		// 保存最大序号的文件
//		File maxFile = null;
//		int maxNum = -1;
//
//		// 检查目录是否存在
//		if (folder.exists() && folder.isDirectory()) {
//			File[] files = folder.listFiles();
//			if (files != null) {
//				for (File file : files) {
//					if (file.isFile()) {
//						Matcher matcher = pattern.matcher(file.getName());
//						if (matcher.matches()) {
//							// 序号
//							String str = matcher.group();
//							int start = str.indexOf("_");
//							int end = str.lastIndexOf(".");
//							String xh = str.substring(start + 1, end);
//							// 找到最大序号
//							int num = Integer.parseInt(xh);
//							if (num > maxNum) {
//								maxNum = num;
//								maxFile = file;
//							}
//						}
//					}
//				}
//				if (maxFile != null) {
//					if (maxNum > 1) {
//						File file = new File(imgFile + String.format(imgName, FactoryUtils.getDate(rq, "yyyyMMdd"), maxNum - 1));
//						file.delete();
//					}
//					print("文件：" + maxFile.getName());
//				} else {
//					print("没有匹配文件");
//				}
//			} else {
//				print("文件夹内容为空");
//			}
//		} else {
//			print("文件夹不存在");
//		}
//		return maxNum;
//	}
//}
