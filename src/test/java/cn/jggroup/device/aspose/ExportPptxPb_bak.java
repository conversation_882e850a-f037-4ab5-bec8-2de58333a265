//package cn.jggroup.device.aspose;
//
//import com.aspose.slides.License;
//import com.aspose.slides.Presentation;
//import com.aspose.slides.SaveFormat;
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//public class ExportPptxPb_bak {
//	private static final boolean dbFlag = true;
//	private static final String lic = getBasePathBy() + "license.xml";
//	private static final String tplFile = getBasePathBy() + "paiban.pptx";
//	private static final String tplName = "周例会工作汇报_%1$s.pptx";
//	private static final String imgFile = getBasePathBy();
//	private static final String imgName = "%1$s_%2$s.png";
//	private static final String imgRegex = "^%1$s_\\d+.png";
//	public static Map<String, String[]> tables = new HashMap<String, String[]>();
//	public static Map<String, String[]> colsCn = new HashMap<String, String[]>();
//	public static Map<String, String[]> colsEn = new HashMap<String, String[]>();
//	public static Map<String, String[]> colsXh = new HashMap<String, String[]>();
//
//	static {
//		// 验证授权
//		license();
//
//		// 处室
//		tables.put("T1", new String[] { "调控处", "MW_APP.NR_OMS_ZPTH_DKCTB" });
//		tables.put("T2", new String[] { "计划处", "MW_APP.NR_OMS_ZPTH_JHCTB" });
//		tables.put("T3", new String[] { "系统处", "MW_APP.NR_OMS_ZPTH_XTCTB" });
//		tables.put("T4", new String[] { "保护处", "MW_APP.NR_OMS_ZPTH_BHCTB" });
//		tables.put("T5", new String[] { "自动化处", "MW_APP.NR_OMS_ZPTH_ZDHCTB" });
//		tables.put("T6", new String[] { "通信处", "MW_APP.NR_OMS_ZPTH_TXCTB" });
//		tables.put("T7", new String[] { "网安处", "MW_APP.NR_OMS_ZPTH_WACTB" });
//		tables.put("T8", new String[] { "现货处", "MW_APP.NR_OMS_ZPTH_XHCTB" });
//		tables.put("T9", new String[] { "综合处", "MW_APP.NR_OMS_ZPTH_ZHCTB" });
//		tables.put("T10", new String[] { "水新处", "MW_APP.NR_OMS_ZPTH_SXCTB" });
//		// 图表
//		tables.put("T11", new String[]{"本周检修计划安排1", "MW_APP.NR_OMS_ZPTH_JHCTB_T1"});
//		tables.put("T12", new String[]{"检修计划开展情况2", "MW_APP.NR_OMS_ZPTH_JHCTB_T2"});
//		tables.put("T13", new String[]{"检修计划开展情况3", "MW_APP.NR_OMS_ZPTH_JHCTB_T3"});
//		tables.put("T14", new String[]{"二次作业风险表", "MW_APP.NR_OMS_ZPTH_GCTB_ZYFX"});
//		// 流程
//		tables.put("T15", new String[]{"周例会流程", "MW_APP.MWT_UD_ZLHLC"});
//		// 排版
//		tables.put("T16", new String[]{"周例会排版", "MW_APP.MWT_UD_ZLHPB"});
//
//		// 填报项-中文名
//		colsCn.put("T1", new String[] { "电网运行情况", "电网运行情况-重点工作", "上周山西电网设备停运情况统计", "上周省网最大负荷" });
//		colsCn.put("T2", new String[] { "电网运行情况", "电力平衡方面", "本周检修计划安排", "月检修计划安排", "重点工作", "上周山西电网检修计划执行情况统计", "本周山西电网检修计划" });
//		colsCn.put("T3", new String[] { "电网运行情况", "本周检修计划安排", "重点工作", "新设备安排情况统计" });
//		colsCn.put("T4", new String[] { "二次作业风险", "重点工作" });
//		colsCn.put("T5", new String[] { "二次作业风险", "重点工作" });
//		colsCn.put("T6", new String[] { "二次作业风险", "重点工作" });
//		colsCn.put("T7", new String[] { "二次作业风险", "重点工作" });
//		colsCn.put("T8", new String[] { "重点工作" });
//		colsCn.put("T9", new String[] { "重点工作" });
//		colsCn.put("T10", new String[] { "重点工作" });
//
//		// 填报项-英文名
//		colsEn.put("T1", new String[] { "dkc_dwyxqk", "dkc_dwyxqk_zdgz", "dkc_szsxdwsbtyqktj", "dkc_szswzdfh" });
//		colsEn.put("T2", new String[] { "jhc_dwyxqk", "jhc_dlphfb", "jhc_bzjxjhap", "jhc_jxjhap", "jhc_zdgz", "jhc_szsxdwjxjhzxqktj", "jhc_bzsxdwjxjh" });
//		colsEn.put("T3", new String[] { "xtc_dwyxqk", "xtc_bzjxjhap", "xtc_zdgz", "xtc_xsbapqktj" });
//		colsEn.put("T4", new String[] { "bhc_eczyfx", "bhc_zdgz" });
//		colsEn.put("T5", new String[] { "zdhc_eczyfx", "zdhc_zdgz" });
//		colsEn.put("T6", new String[] { "txc_eczyfx", "txc_zdgz" });
//		colsEn.put("T7", new String[] { "wac_eczyfx", "wac_zdgz" });
//		colsEn.put("T8", new String[] { "xhc_zdgz" });
//		colsEn.put("T9", new String[] { "zhc_zdgz" });
//		colsEn.put("T10", new String[] { "sxc_zdgz" });
//
//		// 段落+序号
//		colsXh.put("vf", new String[]{"龘", "骉", "魑", "魅", "魍", "魉"});
//		colsXh.put("txt1", new String[]{"zdgz"});
//		colsXh.put("keys", new String[]{"dwyxqk1", "dwyxqk2", "dlphfb", "bzjxjhap1", "bzjxjhap2", "eczyfx", "jxjhap", "zdgz", "szsxdwsbtyqktj", "szsxdwjxjhzxqktj", "bzsxdwjxjh", "xsbapqktj", });
//	}
//
//	// Mock data method to replace MwUtils.queryList
//	private static List<Map<String, Object>> getMockData(String sql) {
//		List<Map<String, Object>> mockData = new ArrayList<>();
//
//		// Mock data for T11 (本周检修计划安排1)
//		if (sql.contains("NR_OMS_ZPTH_JHCTB_T1")) {
//			Map<String, Object> row1 = new HashMap<>();
//			row1.put("xh", "1");
//			row1.put("dw", "太原供电公司");
//			row1.put("jxsb", "110kV变电站主变检修");
//			row1.put("jxsj", "2024-06-03 08:00-17:00");
//			row1.put("dwdj", "低风险");
//			row1.put("bz", "例行检修");
//			mockData.add(row1);
//
//			Map<String, Object> row2 = new HashMap<>();
//			row2.put("xh", "2");
//			row2.put("dw", "长治供电公司");
//			row2.put("jxsb", "220kV输电线路检修");
//			row2.put("jxsj", "2024-06-04 09:00-16:00");
//			row2.put("dwdj", "中风险");
//			row2.put("bz", "紧急抢修");
//			mockData.add(row2);
//		}
//
//		// Mock data for T14 (二次作业风险表)
//		else if (sql.contains("NR_OMS_ZPTH_GCTB_ZYFX")) {
//			Map<String, Object> row1 = new HashMap<>();
//			row1.put("xh", "1");
//			row1.put("dw", "运城供电公司");
//			row1.put("ztxmmc", "继电保护装置检验");
//			row1.put("zysj", "2024-06-03 14:00-16:00");
//			row1.put("zyfxdj", "中风险");
//			row1.put("bz", "定期检验");
//			mockData.add(row1);
//		}
//
//		// Mock data for T16 (周例会排版)
//		else if (sql.contains("MWT_UD_ZLHPB")) {
//			Map<String, Object> row = new HashMap<>();
//			row.put("nr", "本周工作重点：完成年度检修计划的30%，确保电网安全稳定运行。");
//			mockData.add(row);
//		}
//
//		return mockData;
//	}
//
//	public static void main(String[] args) {
//		try {
//			print("模板文件：" + tplFile);
//			Date vDay = new Date(); // Use current date for testing
//			String name = getBasePathBy() + String.format(tplName, "20240603");
//			Presentation pres = export(vDay);
//			pres.save(name, SaveFormat.Pptx);
//			print("导出：" + name);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
//
//	private static String getBasePathBy() {
//		String basePath = "D:/logs/zlh/";
//		String os = System.getenv("os");
//		if (!("Windows_NT".equals(os))) {
//			basePath = "/home/<USER>/Portal/PROJECT-HOME/business/sxd5000/zhjs/zlh/out/";
//		}
//		return basePath;
//	}
//
//	private static void license() {
//		print("Aspose授权文件：" + lic);
//		License license = new License();
//		try {
//			license.setLicense(lic);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
//
//	public static void print(String msg) {
//		System.out.println(msg);
//	}
//
//	public static Presentation export(Date date) throws IOException {
//		// Implementation using mock data...
//		// Replace all mwUtils.queryList calls with getMockData
//		return null; // TODO: Implement actual export logic
//	}
//}
