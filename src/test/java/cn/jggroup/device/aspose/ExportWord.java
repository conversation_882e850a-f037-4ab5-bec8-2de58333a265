package cn.jggroup.device.aspose;

import cn.jggroup.device.uitls.FactoryUtils;
import com.aspose.words.DataRow;
import com.aspose.words.DataTable;
import com.aspose.words.Document;
import com.aspose.words.DocumentBuilder;
import com.aspose.words.Font;
import com.aspose.words.FontSettings;
import com.aspose.words.License;
import com.aspose.words.MailMerge;
import com.aspose.words.Node;
import com.aspose.words.NodeCollection;
import com.aspose.words.NodeType;
import com.aspose.words.PageSetup;
import com.aspose.words.Paragraph;
import com.aspose.words.RelativeHorizontalPosition;
import com.aspose.words.RelativeVerticalPosition;
import com.aspose.words.Run;
import com.aspose.words.SaveFormat;
import com.aspose.words.Shape;
import com.sxoms.utils.MwUtils;
import com.sxoms.utils.OSinfo;
import java.awt.Color;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;

public class ExportWordPb {
    //true.访问真实库 false.访问测试库
    private static final boolean dbFlag = true;
    private static MwUtils mwUtils = new MwUtils(dbFlag);
    public static Map<String, String[]> tables = new HashMap<String, String[]>();
    public static Map<String, String[]> colxhs = new HashMap<String, String[]>();
    public static Map<String, String[]> highlights = new HashMap<String, String[]>();
    public static Map<String, Map<String, Object>> drawRuns = new HashMap<String, Map<String, Object>>();
    private static final String lic = getBasePathBy() + "license.xml";
    private static final String tplFile = getBasePathBy() + "paiban.doc";
    private static final String tplName = "周例会工作汇报_%1$s.docx";
    private static final String imgFile = getBasePathBy();
    private static final String imgName = "%1$s_%2$s.png";
    private static final String imgRegex = "^%1$s_\\d+.png";

    static {
        // 处室
        tables.put("T1", new String[]{"调控处", "MW_APP.NR_OMS_ZPTH_DKCTB"});
        tables.put("T2", new String[]{"计划处", "MW_APP.NR_OMS_ZPTH_JHCTB"});
        tables.put("T3", new String[]{"系统处", "MW_APP.NR_OMS_ZPTH_XTCTB"});
        tables.put("T4", new String[]{"保护处", "MW_APP.NR_OMS_ZPTH_BHCTB"});
        tables.put("T5", new String[]{"自动化处", "MW_APP.NR_OMS_ZPTH_ZDHCTB"});
        tables.put("T6", new String[]{"通信处", "MW_APP.NR_OMS_ZPTH_TXCTB"});
        tables.put("T7", new String[]{"网安处", "MW_APP.NR_OMS_ZPTH_WACTB"});
        tables.put("T8", new String[]{"现货处", "MW_APP.NR_OMS_ZPTH_XHCTB"});
        tables.put("T9", new String[]{"综合处", "MW_APP.NR_OMS_ZPTH_ZHCTB"});
        tables.put("T10", new String[]{"水新处", "MW_APP.NR_OMS_ZPTH_SXCTB"});
        // 图表
        tables.put("T11", new String[]{"本周检修计划安排1", "MW_APP.NR_OMS_ZPTH_JHCTB_T1"});
        tables.put("T12", new String[]{"检修计划开展情况2", "MW_APP.NR_OMS_ZPTH_JHCTB_T2"});
        tables.put("T13", new String[]{"检修计划开展情况3", "MW_APP.NR_OMS_ZPTH_JHCTB_T3"});
        tables.put("T14", new String[]{"二次作业风险表", "MW_APP.NR_OMS_ZPTH_GCTB_ZYFX"});
        // 流程
        tables.put("T15", new String[]{"周例会流程", "MW_APP.MWT_UD_ZLHLC"});
        // 排版
        tables.put("T16", new String[]{"周例会排版", "MW_APP.MWT_UD_ZLHPB"});

        // 龘, 骉, 魑, 魍：分别对应4种样式
        colxhs.put("vf", new String[]{"龘", "骉", "魑", "魅", "魍", "魉"});
        colxhs.put("txt1", new String[]{"zdgz"});
        colxhs.put("txt2", new String[]{"szsxdwjxjhzxqktj"});
        colxhs.put("txt3", new String[]{"bzsxdwjxjh"});
        colxhs.put("txt4", new String[]{"xsbapqktj"});
        colxhs.put("txt2_split", new String[]{"检修.*\\d+项", "500千伏.*\\d+项", "220千伏.*\\d+项", "110千伏.*\\d+项", "35千伏.*\\d+项", "10千伏.*\\d+项",});
        colxhs.put("txt3_split", new String[]{"检修.*\\d+项", "500千伏.*\\d+项", "220千伏.*\\d+项", "110千伏.*\\d+项", "35千伏.*\\d+项", "10千伏.*\\d+项",});
        colxhs.put("txt4_split", new String[]{"启动.*\\d+项", "启.*\\d+项", });
        colxhs.put("keys", new String[]{"dwyxqk1", "dwyxqk2", "dlphfb", "bzjxjhap1", "bzjxjhap2",  "eczyfx", "jxjhap", "zdgz", "szsxdwsbtyqktj", "szsxdwjxjhzxqktj", "bzsxdwjxjh", "xsbapqktj", });

        // 文字样式
        drawRuns.put("s01", new HashMap<String, Object>() {{ put("bold", false);put("size", 16.0D);put("color", Color.BLACK);put("name", "仿宋_GB2312"); }});
        drawRuns.put("s02", new HashMap<String, Object>() {{ put("bold", true);put("size", 16.0D);put("color", Color.BLACK);put("name", "仿宋_GB2312"); }});
        drawRuns.put("s11", new HashMap<String, Object>() {{ put("bold", false);put("size", 16.0D);put("color", Color.BLACK);put("name", "仿宋_GB2312"); }});
        drawRuns.put("s12", new HashMap<String, Object>() {{ put("bold", true);put("size", 16.0D);put("color", Color.BLACK);put("name", "仿宋_GB2312"); }});
        drawRuns.put("s21", new HashMap<String, Object>() {{ put("bold", false);put("size", 16.0D);put("color", Color.BLACK);put("name", "仿宋_GB2312"); }});
        drawRuns.put("s22", new HashMap<String, Object>() {{ put("bold", true);put("size", 16.0D);put("color", Color.BLACK);put("name", "仿宋_GB2312"); }});
        drawRuns.put("s31", new HashMap<String, Object>() {{ put("bold", false);put("size", 14.0D);put("color", Color.BLACK);put("name", "仿宋_GB2312"); }});
        drawRuns.put("s32", new HashMap<String, Object>() {{ put("bold", true);put("size", 14.0D);put("color", Color.BLACK);put("name", "仿宋_GB2312"); }});
        drawRuns.put("s41", new HashMap<String, Object>() {{ put("bold", false);put("size", 14.0D);put("color", Color.BLACK);put("name", "仿宋_GB2312"); }});
        drawRuns.put("s42", new HashMap<String, Object>() {{ put("bold", true);put("size", 14.0D);put("color", Color.BLACK);put("name", "仿宋_GB2312"); }});
        drawRuns.put("s51", new HashMap<String, Object>() {{ put("bold", false);put("size", 14.0D);put("color", Color.BLACK);put("name", "仿宋_GB2312"); }});
        drawRuns.put("s52", new HashMap<String, Object>() {{ put("bold", true);put("size", 14.0D);put("color", Color.BLACK);put("name", "仿宋_GB2312"); }});

        // 加粗关键字
        highlights.put("keywords", new String[]{
                "原因", "省网用电", "公司外送", "雁淮直流", "超高压变电", "重点生产工作", "重点基建工作", "建设分公司", "输电", "各地调",
                "太原", "大同", "阳泉", "长治", "晋城", "朔州", "晋中", "运城", "忻州", "临汾", "吕梁",
                "500千伏及以上\\d+项", "500千伏\\d+项", "220千伏\\d+项", "110千伏\\d+项", "35千伏\\d+项", "10千伏\\d+项",
                "太原\\d+项", "大同\\d+项", "阳泉\\d+项", "长治\\d+项", "晋城\\d+项", "朔州\\d+项", "晋中\\d+项", "运城\\d+项", "忻州\\d+项", "临汾\\d+项", "吕梁\\d+项",
                "上周，全省安排计划检修工作\\d+项", "本周安排计划检修工作\\d+项", "超高压变电\\d+项", "上周主网", "上周配网", "完成新设备启动\\d+项", "本周主网安排新设备启动\\d+项", "本周配网安排新设备启动\\d+项",
        });
    }

    /**
     * 打印消息
     */
    public static void print(Object msg) {
        System.out.println(msg);
    }

    /**
     * 打印消息
     */
    public static void error(String msg) {
        System.err.println(msg);
    }

    /**
     * Aspose组件注册
     */
    private static void license() {
        print("Aspose授权文件：" + lic);
        License license = new License();
        try {
            license.setLicense(lic);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 路径
     */
    private static String getBasePathBy() {
        String basePath = "D:/logs/zlh/";
        String os = System.getenv("os");
        if (!("Windows_NT".equals(os))) {
            basePath = "/home/<USER>/Portal/PROJECT-HOME/business/sxd5000/zhjs/zlh/out/";
        }
        return basePath;
    }

    /**
     * GET请求参数
     */
    public static Map<String, Object> getDto(HttpServletRequest request) {
        Map<String, Object> map = new HashMap<String, Object>();
        Enumeration keys = request.getParameterNames();
        while (keys.hasMoreElements()) {
            String key = (String) keys.nextElement();
            String val = request.getParameter(key);
            if (key != null && !"".equals(key)) {
                map.put(key, val);
            }
        }
        return map;
    }

    /******************************************************************************************************************/

    public static void main(String[] args) {
        try {
            print("模板：" + tplFile);
            String rq = "2024-06-03";
            Document doc = export(rq);
            String path = getBasePathBy() + String.format(tplName, FactoryUtils.getDate(FactoryUtils.getToday(), "yyyyMMdd"));

            // 自动更新样式
//			doc.updateFields();
//			doc.updateListLabels();
//			doc.updateWordCount();
//			doc.updatePageLayout();
//			doc.setAutomaticallyUpdateSyles(true);
//          doc.save(path, SaveFormat.PDF);

            doc.save(path, SaveFormat.DOCX);
            print("导出：" + path);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /******************************************************************************************************************/

    public static Document export(String rq) {
        // 构建文字
        Map<String, Object> kv = new HashMap<String, Object>();
        buildText(kv, rq);
        buildTextSplit(kv);
        kv.put("rq", FactoryUtils.getDate(FactoryUtils.getToday(), "yyyy年MM月dd日"));
        kv.put("cs", "调控中心");
        kv.put("vMonth", Integer.valueOf(FactoryUtils.getDate(rq, "dd")) > 15 ? (Integer.valueOf(FactoryUtils.getDate(rq, "MM")) + 1) : (Integer.valueOf(FactoryUtils.getDate(rq, "MM"))));
        String[] keys = kv.keySet().toArray(new String[]{});
        Object[] vals = new Object[keys.length];
        for (int i = 0; i < keys.length; i++) {
            vals[i] = kv.get(keys[i]);
        }

        // 构建表格
        List<DataTable> dtList = buildTable(rq);

        // 验证授权
        license();

        // 生成文档
        Document doc = null;
        InputStream is = null;
        try {
            is = new FileInputStream(tplFile);
            doc = new Document(is);
            // 图表
            buildChart(doc, rq);

            // 文本
            MailMerge mm = doc.getMailMerge();
            mm.execute(keys, vals);

            // 样式
            buildStyle(doc);

            // 表格
            for (DataTable dt : dtList) {
                mm.executeWithRegions(dt);
            }

            // 删除未使用的空白域
            mm.deleteFields();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    print(e);
                }
            }
        }
        return doc;
    }

    /******************************************************************************************************************/

    // 第一步：构建文字
    public static void buildText(Map<String, Object> result, String rq) {
        String[] keys = colxhs.get("keys");
        for (int k = 0; k < keys.length; k++) {
            String sql = "SELECT NR FROM " + tables.get("T16")[1] + " WHERE LX = '" + keys[k] + "' AND RQ = '" + rq + "'";
            // print("SQL：" + sql);
            List<Map<String, Object>> ls = mwUtils.queryList(sql);
            String rs = "";
            if (ls.size() > 0) {
                rs = (String) ls.get(0).get("nr");
            }
            result.put(keys[k], rs);
        }
    }

    // 处理文字前缀，用于标记处理的特殊段落
    // 填报项，不带序号；拼接为 【前缀（龘）+文本】 + 【\n】 + 【前缀（龘）+文本】
    public static void buildTextSplit(Map<String, Object> result) {
        String[] keys = colxhs.get("keys");
        String[] vf = colxhs.get("vf");
        String[] txt1 = colxhs.get("txt1");
        String[] txt2 = colxhs.get("txt2");
        String[] txt3 = colxhs.get("txt3");
        String[] txt4 = colxhs.get("txt4");
        String[] txt2_split = colxhs.get("txt2_split");
        String[] txt3_split = colxhs.get("txt3_split");
        String[] txt4_split = colxhs.get("txt4_split");
        for (int i = 0; i < keys.length; i++) {
            String key = keys[i];
            String value = (String) result.get(key);
            if (!"".equals(value) && value != null) {
                //
                value = value.trim();
                value = value.replaceAll("\n+", "\n").replaceAll("\r+", "\r");
                //
                String[] split = value.split("\r|\n");
                //
                boolean bFlag = false;
                for (int j = 0; j < txt1.length; j++) {
                    if (txt1[j].equals(key)) {
                        bFlag = true;
                        break;
                    }
                }
                //
                boolean vFlag = false;
                for (int j = 0; j < txt2.length; j++) {
                    if (txt2[j].equals(key)) {
                        vFlag = true;
                        break;
                    }
                }
                //
                boolean dFlag = false;
                for (int j = 0; j < txt3.length; j++) {
                    if (txt3[j].equals(key)) {
                        dFlag = true;
                        break;
                    }
                }
                //
                boolean lFlag = false;
                for (int j = 0; j < txt4.length; j++) {
                    if (txt4[j].equals(key)) {
                        lFlag = true;
                        break;
                    }
                }
                if (vFlag) {
                    for (int j = 0; j < split.length; j++) {
                        if (j == 0) {
                            value = vf[0] + split[0];
                        } else {
                            boolean kFlag = false;
                            for (String keyWord : txt2_split) {
                                Set<String> keyWordSet = FactoryUtils.invokeRegxAll(split[j], keyWord);
                                for (String kw: keyWordSet ) {
                                    int indexOf = split[j].indexOf(kw);
                                    if (indexOf > -1) {
                                        kFlag = true;
                                        break;
                                    }
                                }
                            }
                            if (kFlag) {
                                value = value + "\n" + vf[0] + split[j];
                            } else {
                                value = value + "\n" + vf[3] + split[j];
                            }
                        }

                    }
                } else if (dFlag) {
                    for (int j = 0; j < split.length; j++) {
                        if (j == 0) {
                            value = vf[0] + split[0];
                        } else {
                            boolean kFlag = false;
                            for (String keyWord : txt3_split) {
                                Set<String> keyWordSet = FactoryUtils.invokeRegxAll(split[j], keyWord);
                                for (String kw: keyWordSet ) {
                                    int indexOf = split[j].indexOf(kw);
                                    if (indexOf > -1) {
                                        kFlag = true;
                                        break;
                                    }
                                }
                            }
                            if (kFlag) {
                                value = value + "\n" + vf[0] + split[j];
                            } else {
                                value = value + "\n" + vf[4] + split[j];
                            }
                        }

                    }
                } else if (lFlag) {
                    for (int j = 0; j < split.length; j++) {
                        if (j == 0) {
                            value = vf[0] + split[0];
                        } else {
                            boolean kFlag = false;
                            for (String keyWord : txt4_split) {
                                Set<String> keyWordSet = FactoryUtils.invokeRegxAll(split[j], keyWord);
                                for (String kw: keyWordSet ) {
                                    int indexOf = split[j].indexOf(kw);
                                    if (indexOf > -1) {
                                        kFlag = true;
                                        break;
                                    }
                                }
                            }
                            if (kFlag) {
                                value = value + "\n" + vf[0] + split[j];
                            } else {
                                value = value + "\n" + vf[5] + split[j];
                            }
                        }
                    }
                } else if (bFlag) {
                    for (int j = 0; j < split.length; j++) {
                        if (j == 0) {
                            value = vf[1] + split[0];
                        } else {
                            value = value + "\n" + vf[1] + split[j];
                        }
                    }
                } else {
                    for (int j = 0; j < split.length; j++) {
                        if (j == 0) {
                            value = vf[0] + split[0];
                        } else {
                            value = value + "\n" + vf[0] + split[j];
                        }
                    }
                }
                result.put(key, value);
            } else {
                result.put(key, vf[0]);
            }
        }
    }

    /******************************************************************************************************************/

    // 第二步：构建表格
    public static List<DataTable> buildTable(String rq) {
        List<DataTable> ls = new ArrayList<DataTable>();
        ls.add(buildDt(query11(rq, 11), "bzjxjh"));
        ls.add(buildDt(query12Or13(rq, 12), "jxjh1"));
        ls.add(buildDt(query12Or13(rq, 13), "jxjh2"));
        ls.add(buildDt(query14(rq, 14), "zyfx"));
        return ls;
    }

    // 查询T11
    public static List<Map<String, Object>> query11(String rq, int tabIdx) {
        List<Map<String, Object>> result = null;
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT * FROM ").append(tables.get("T" + tabIdx)[1]).append(" WHERE RQ = '" + rq + "' ORDER BY CAST(XH AS NUMBER)");
//        print("SQL：" + sql);
        result = mwUtils.queryList(sql);
        result = formatList(result, new String[]{"dw","jxsb","jxsj","dwdj","bz",});
        return result;
    }

    // 查询T12、13
    public static List<Map<String, Object>> query12Or13(String rq, int tabIdx) {
        List<Map<String, Object>> result = null;
        StringBuffer sql = new StringBuffer();
        sql.append("WITH stat AS (");
        sql.append("    SELECT RQ,DQ,WKV,EKV,YKV,SKV,TKV,ROW_NUMBER() OVER(PARTITION BY RQ ORDER BY DECODE(DQ, '太原', 1, '大同', 2, '朔州', 3, '忻州', 4, '阳泉', 5, '晋中', 6, '吕梁', 7, '长治', 8, '晋城', 9, '临汾', 10, '运城', 11, '超高压变电', 12, '超高压输电', 13, '系统外', 14)) AS R FROM (SELECT RQ,DQ,WKV,EKV,YKV,SKV,TKV FROM " + tables.get("T" + tabIdx)[1] + " WHERE RQ = '" + rq + "') t");
        sql.append("), WKV AS (");
        sql.append("    SELECT '500千伏及以上' AS DQ, tmp.*, tmp.TY + tmp.DT + tmp.SZ + tmp.XZ + tmp.YQ + tmp.JZ + tmp.LL + tmp.CZ + tmp.JC + tmp.LF + tmp.YC + tmp.SJX + tmp.SSD + tmp.XTW AS HJ FROM (SELECT CAST(MAX(DECODE(stat.R,1,stat.WKV)) AS INT) AS TY,CAST(MAX(DECODE(stat.R,2,stat.WKV)) AS INT) AS DT,CAST(MAX(DECODE(stat.R,3,stat.WKV)) AS INT) AS SZ,CAST(MAX(DECODE(stat.R,4,stat.WKV)) AS INT) AS XZ,CAST(MAX(DECODE(stat.R,5,stat.WKV)) AS INT) AS YQ,CAST(MAX(DECODE(stat.R,6,stat.WKV)) AS INT) AS JZ,CAST(MAX(DECODE(stat.R,7,stat.WKV)) AS INT) AS LL,CAST(MAX(DECODE(stat.R,8,stat.WKV)) AS INT) AS CZ,CAST(MAX(DECODE(stat.R,9,stat.WKV)) AS INT) AS JC,CAST(MAX(DECODE(stat.R,10,stat.WKV)) AS INT) AS LF,CAST(MAX(DECODE(stat.R,11,stat.WKV)) AS INT) AS YC,CAST(MAX(DECODE(stat.R,12,stat.WKV)) AS INT) AS SJX,CAST(MAX(DECODE(stat.R,13,stat.WKV)) AS INT) AS SSD,CAST(MAX(DECODE(stat.R,14,stat.WKV)) AS INT) AS XTW FROM stat GROUP BY RQ) tmp");
        sql.append("), EKV AS (");
        sql.append("    SELECT '220千伏' AS DQ, tmp.*, tmp.TY + tmp.DT + tmp.SZ + tmp.XZ + tmp.YQ + tmp.JZ + tmp.LL + tmp.CZ + tmp.JC + tmp.LF + tmp.YC + tmp.SJX + tmp.SSD + tmp.XTW AS HJ FROM (SELECT CAST(MAX(DECODE(stat.R,1,stat.EKV)) AS INT) AS TY,CAST(MAX(DECODE(stat.R,2,stat.EKV)) AS INT) AS DT,CAST(MAX(DECODE(stat.R,3,stat.EKV)) AS INT) AS SZ,CAST(MAX(DECODE(stat.R,4,stat.EKV)) AS INT) AS XZ,CAST(MAX(DECODE(stat.R,5,stat.EKV)) AS INT) AS YQ,CAST(MAX(DECODE(stat.R,6,stat.EKV)) AS INT) AS JZ,CAST(MAX(DECODE(stat.R,7,stat.EKV)) AS INT) AS LL,CAST(MAX(DECODE(stat.R,8,stat.EKV)) AS INT) AS CZ,CAST(MAX(DECODE(stat.R,9,stat.EKV)) AS INT) AS JC,CAST(MAX(DECODE(stat.R,10,stat.EKV)) AS INT) AS LF,CAST(MAX(DECODE(stat.R,11,stat.EKV)) AS INT) AS YC,CAST(MAX(DECODE(stat.R,12,stat.EKV)) AS INT) AS SJX,CAST(MAX(DECODE(stat.R,13,stat.EKV)) AS INT) AS SSD,CAST(MAX(DECODE(stat.R,14,stat.EKV)) AS INT) AS XTW FROM stat GROUP BY RQ) tmp");
        sql.append("), YKV AS (");
        sql.append("    SELECT '110千伏' AS DQ, tmp.*, tmp.TY + tmp.DT + tmp.SZ + tmp.XZ + tmp.YQ + tmp.JZ + tmp.LL + tmp.CZ + tmp.JC + tmp.LF + tmp.YC + tmp.SJX + tmp.SSD + tmp.XTW AS HJ FROM (SELECT CAST(MAX(DECODE(stat.R,1,stat.YKV)) AS INT) AS TY,CAST(MAX(DECODE(stat.R,2,stat.YKV)) AS INT) AS DT,CAST(MAX(DECODE(stat.R,3,stat.YKV)) AS INT) AS SZ,CAST(MAX(DECODE(stat.R,4,stat.YKV)) AS INT) AS XZ,CAST(MAX(DECODE(stat.R,5,stat.YKV)) AS INT) AS YQ,CAST(MAX(DECODE(stat.R,6,stat.YKV)) AS INT) AS JZ,CAST(MAX(DECODE(stat.R,7,stat.YKV)) AS INT) AS LL,CAST(MAX(DECODE(stat.R,8,stat.YKV)) AS INT) AS CZ,CAST(MAX(DECODE(stat.R,9,stat.YKV)) AS INT) AS JC,CAST(MAX(DECODE(stat.R,10,stat.YKV)) AS INT) AS LF,CAST(MAX(DECODE(stat.R,11,stat.YKV)) AS INT) AS YC,CAST(MAX(DECODE(stat.R,12,stat.YKV)) AS INT) AS SJX,CAST(MAX(DECODE(stat.R,13,stat.YKV)) AS INT) AS SSD,CAST(MAX(DECODE(stat.R,14,stat.YKV)) AS INT) AS XTW FROM stat GROUP BY RQ) tmp");
        sql.append("), SKV AS (");
        sql.append("    SELECT '35千伏' AS DQ, tmp.*, tmp.TY + tmp.DT + tmp.SZ + tmp.XZ + tmp.YQ + tmp.JZ + tmp.LL + tmp.CZ + tmp.JC + tmp.LF + tmp.YC + tmp.SJX + tmp.SSD + tmp.XTW AS HJ FROM (SELECT CAST(MAX(DECODE(stat.R,1,stat.SKV)) AS INT) AS TY,CAST(MAX(DECODE(stat.R,2,stat.SKV)) AS INT) AS DT,CAST(MAX(DECODE(stat.R,3,stat.SKV)) AS INT) AS SZ,CAST(MAX(DECODE(stat.R,4,stat.SKV)) AS INT) AS XZ,CAST(MAX(DECODE(stat.R,5,stat.SKV)) AS INT) AS YQ,CAST(MAX(DECODE(stat.R,6,stat.SKV)) AS INT) AS JZ,CAST(MAX(DECODE(stat.R,7,stat.SKV)) AS INT) AS LL,CAST(MAX(DECODE(stat.R,8,stat.SKV)) AS INT) AS CZ,CAST(MAX(DECODE(stat.R,9,stat.SKV)) AS INT) AS JC,CAST(MAX(DECODE(stat.R,10,stat.SKV)) AS INT) AS LF,CAST(MAX(DECODE(stat.R,11,stat.SKV)) AS INT) AS YC,CAST(MAX(DECODE(stat.R,12,stat.SKV)) AS INT) AS SJX,CAST(MAX(DECODE(stat.R,13,stat.SKV)) AS INT) AS SSD,CAST(MAX(DECODE(stat.R,14,stat.SKV)) AS INT) AS XTW FROM stat GROUP BY RQ) tmp");
        sql.append("), TKV AS (");
        sql.append("    SELECT '10千伏' AS DQ, tmp.*, tmp.TY + tmp.DT + tmp.SZ + tmp.XZ + tmp.YQ + tmp.JZ + tmp.LL + tmp.CZ + tmp.JC + tmp.LF + tmp.YC + tmp.SJX + tmp.SSD + tmp.XTW AS HJ FROM (SELECT CAST(MAX(DECODE(stat.R,1,stat.TKV)) AS INT) AS TY,CAST(MAX(DECODE(stat.R,2,stat.TKV)) AS INT) AS DT,CAST(MAX(DECODE(stat.R,3,stat.TKV)) AS INT) AS SZ,CAST(MAX(DECODE(stat.R,4,stat.TKV)) AS INT) AS XZ,CAST(MAX(DECODE(stat.R,5,stat.TKV)) AS INT) AS YQ,CAST(MAX(DECODE(stat.R,6,stat.TKV)) AS INT) AS JZ,CAST(MAX(DECODE(stat.R,7,stat.TKV)) AS INT) AS LL,CAST(MAX(DECODE(stat.R,8,stat.TKV)) AS INT) AS CZ,CAST(MAX(DECODE(stat.R,9,stat.TKV)) AS INT) AS JC,CAST(MAX(DECODE(stat.R,10,stat.TKV)) AS INT) AS LF,CAST(MAX(DECODE(stat.R,11,stat.TKV)) AS INT) AS YC,CAST(MAX(DECODE(stat.R,12,stat.TKV)) AS INT) AS SJX,CAST(MAX(DECODE(stat.R,13,stat.TKV)) AS INT) AS SSD,CAST(MAX(DECODE(stat.R,14,stat.TKV)) AS INT) AS XTW FROM stat GROUP BY RQ) tmp");
        sql.append("), tj AS (");
        sql.append("    SELECT * FROM WKV UNION ALL (SELECT * FROM EKV) UNION ALL (SELECT * FROM YKV) UNION ALL (SELECT * FROM SKV) UNION ALL (SELECT * FROM TKV)");
        sql.append("), kj AS (");
        sql.append("    SELECT '合计' AS DQ, SUM(TY) AS TY, SUM(DT) AS DT, SUM(SZ) AS SZ, SUM(XZ) AS XZ, SUM(YQ) AS YQ, SUM(JZ) AS JZ, SUM(LL) AS LL, SUM(CZ) AS CZ, SUM(JC) AS JC, SUM(LF) AS LF, SUM(YC) AS YC, SUM(SJX) AS SJX, SUM(SSD) AS SSD, SUM(XTW) AS XTW, SUM(HJ) AS HJ FROM tj");
        sql.append(") SELECT * FROM tj UNION ALL (SELECT * FROM kj);");
//        print("SQL：" + sql);
        result = mwUtils.queryList(sql);
        return result;
    }

    // 查询T14
    public static List<Map<String, Object>> query14(String rq, int tabIdx) {
        List<Map<String, Object>> result = null;
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT * FROM ").append(tables.get("T" + tabIdx)[1]).append(" WHERE RQ = '" + rq + "' ORDER BY CS");
//        print("SQL：" + sql);
        result = mwUtils.queryList(sql);
        result = formatList(result, new String[]{"dw", "ztxmmc", "zysj", "zyfxdj", "bz",});
        return result;
    }

    // 格式化数据
    public static List<Map<String, Object>> formatList(List<Map<String, Object>> result, String[] keys) {
        if (!result.isEmpty()) {
            int vc = 1;
            for (int i = 0; i < result.size(); i++) {
                Map<String, Object> map = result.get(i);
                int count = 0;
                for (String key : keys) {
                    String obj = (String)map.get(key);
                    if (obj == null || "".equals(obj.trim())) {
                        count++;
                    }
                }
                boolean vFlag = count == keys.length;
                if (vFlag) {
                    result.remove(map);
                } else {
                    map.put("xh", String.valueOf(vc++));
                }
            }
        }
        return result;
    }

    // 构建DT
    public static DataTable buildDt(List<Map<String, Object>> list, String dsKey) {
        DataTable dt = new DataTable(dsKey);
        if (list != null && list.size() >= 1) {
            Set<String> keys = list.get(0).keySet();
            for (String key : keys) {
                dt.getColumns().add(key);
            }
            for (int i = 0, len = list.size(); i < len; i++) {
                DataRow dr = dt.newRow();
                Map<String, Object> bean = list.get(i);
                for (String key : keys) {
                    dr.set(key, "null".equals(bean.get(key)) ? " " : bean.get(key));
                }
                dt.getRows().add(dr);
            }
        }
        return dt;
    }

    // 构建DT
    public static DataTable buildDt(Map<String, Object> map, String dsKey) {
        DataTable dt = new DataTable(dsKey);
        if (map != null && !map.isEmpty()) {
            Set<String> keys = map.keySet();
            dt = buildDt(map, keys.toArray(new String[]{}), dsKey);
        }
        return dt;
    }

    // 构建DT
    public static DataTable buildDt(Map<String, Object> map, String[] keys, String dsKey) {
       DataTable dt = new DataTable(dsKey);
        if (map != null && !map.isEmpty()) {
            for (String key : keys) {
                dt.getColumns().add(key);
            }
            DataRow dr = dt.newRow();
            for (String key : keys) {
                dr.set(key, map.get(key));
            }
            dt.getRows().add(dr);
        }
        return dt;
    }

    /******************************************************************************************************************/

    // 第三步：构建图片
    @SuppressWarnings("unchecked")
    public static void buildChart(Document doc, String rq) {
        // 查询图片最大序号
        int xh = queryImgMax(rq);
        DocumentBuilder builder = null;
        try {
            builder = new DocumentBuilder(doc);
            BufferedImage img = null;
            NodeCollection<Node> nodes = doc.getChildNodes(NodeType.PARAGRAPH, true);
            for (int i = 0; i < nodes.getCount(); i++) {
                Node node = nodes.get(i);
                String txt = node.getText().trim();
                // 替换图1上方的图片
                if (txt.endsWith("图1")) {
                    if (xh > -1) {
                        File file = new File(imgFile + String.format(imgName, FactoryUtils.getDate(rq, "yyyyMMdd"), xh));
                        if (file.exists()) {
                            img = ImageIO.read(file);
                            // 获取页面宽度
                            PageSetup pageSetup = builder.getPageSetup();
                            double pageWidth = pageSetup.getPageWidth() - pageSetup.getLeftMargin() - pageSetup.getRightMargin();
                            int imageWidth = img.getWidth();
                            int imageHeight = img.getHeight();
                            // 计算缩放比例
                            double scale = pageWidth / imageWidth;
                            double scaledWidth = imageWidth * scale;
                            double scaledHeight = imageHeight * scale;
                            // 插入图片并调整大小
                            Node vNode = node.getPreviousSibling();
                            builder.moveTo(vNode);
                            Shape shape = builder.insertImage(img, scaledWidth, scaledHeight);
                            shape.setRelativeHorizontalPosition(RelativeHorizontalPosition.CHARACTER);
                            shape.setRelativeVerticalPosition(RelativeVerticalPosition.PARAGRAPH);
                            // 删除图1
                            node.remove();
                        }
                    } else {
                        // 删除图1
                        node.remove();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 查询图片最大序号
    public static int queryImgMax(String rq) {
        // 文件夹路径
        File folder = new File(imgFile);

        // 正则表达式
        String regex = String.format(imgRegex, FactoryUtils.getDate(rq, "yyyyMMdd"));
        Pattern pattern = Pattern.compile(regex);

        // 保存最大序号的文件
        File maxFile = null;
        int maxNum = -1;

        // 检查目录是否存在
        if (folder.exists() && folder.isDirectory()) {
            File[] files = folder.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        Matcher matcher = pattern.matcher(file.getName());
                        if (matcher.matches()) {
                            // 序号
                            String str = matcher.group();
                            int start = str.indexOf("_");
                            int end = str.lastIndexOf(".");
                            String xh = str.substring(start + 1, end);
                            // 找到最大序号
                            int num = Integer.parseInt(xh);
                            if (num > maxNum) {
                                maxNum = num;
                                maxFile = file;
                            }
                        }
                    }
                }
                if (maxFile != null) {
                    if (maxNum > 1) {
                        File file = new File(imgFile + String.format(imgName, FactoryUtils.getDate(rq, "yyyyMMdd"), maxNum - 1));
                        file.delete();
                    }
                    print("文件：" + maxFile.getName());
                } else {
                    print("没有匹配文件");
                }
            } else {
                print("文件夹内容为空");
            }
        } else {
            print("文件夹不存在");
        }
        return maxNum;
    }

    /******************************************************************************************************************/

    // 第四步：构建样式
    public static void buildStyle(Document doc) {
        for (Node node : (Iterable<Node>) doc.getChildNodes(NodeType.PARAGRAPH, true)) {
            Paragraph paragraph = (Paragraph) node;
            String paragraphText = paragraph.getText();

            // 判断样式类型
            int vflag = isType(paragraphText);
//            print(paragraphText);
//            print(vflag);
//            print("");

            // 处理各种样式
            if (vflag > -1) {
                // 将"龘", "骉", "魑", "魍"标志文字删除
                paragraphText = paragraphText.substring(1);
                // 插入标签为空，删除此标签所在行
                if ("\r".equals(paragraphText)) {
                    node.remove();
                }
                // 循环当前段落中的全部关键字，将索引存储起来段落
                List<int[]> pos = findBoldIndex(paragraphText, vflag);
                if (pos.size() > 0) {
                    // 合并重复索引
                    List<int[]> mergedRanges = mergeRanges(pos);
                    // 合并段落：有索引
                    mergeParas(paragraph, mergedRanges, doc, paragraphText, vflag);
                } else {
                    // 合并段落：无索引
                    mergeParas(paragraph, doc, paragraphText, vflag);
                }
            }
        }
    }

    // 判断样式类型
    public static int isType(String paragraphText) {
        String[] vf = colxhs.get("vf");
        int index = -1;
        for (int j = 0; j < vf.length; j++) {
            if (vf[j].equals(paragraphText.substring(0, 1))) {
                index = j;
                break;
            }
        }
        return index;
    }

    // 循环当前段落中的全部关键字，将索引存储起来段落
    public static List<int[]> findBoldIndex(String paragraphText, int vflag) {
        List<int[]> pos = new ArrayList<int[]>();
        // 第一个句号前的文字加粗
        // 骉：填报项-重点工作，带序号
        if (vflag == 1) {
            Matcher matcher = Pattern.compile("^\\d+\\.|^\\d+\\．|^\\d+\\、").matcher(paragraphText);
            if (matcher.find()) {
                int index = paragraphText.indexOf("。");
                if (index > 0) {
                    pos.add(new int[]{0, index});
                }
            }
        }
        // 关键字加粗
        String[] highLightKeys = highlights.get("keywords");
        for (String keyWord : highLightKeys) {
            Set<String> keyWordSet = FactoryUtils.invokeRegxAll(paragraphText,keyWord);
            for (String kw: keyWordSet ) {
                int index = paragraphText.indexOf(kw);
                while (index >= 0) {
                    pos.add(new int[]{index, index + kw.length() - 1});
                    index = paragraphText.indexOf(kw, index + 1);
                }
            }
        }
        return pos;
    }

    // 合并重复索引
    public static List<int[]> mergeRanges(List<int[]> pos) {
        // 如果范围为空，直接返回空列表
        if (pos.isEmpty()) {
            return new ArrayList<int[]>();
        }
        // 对pos按照第一个元素的大小进行排序
        Collections.sort(pos, new Comparator<int[]>() {
            public int compare(int[] a, int[] b) {
                if (a[0] < b[0]) {
                    return -1;
                } else if (a[0] > b[0]) {
                    return 1;
                } else {
                    return 0;
                }
            }
        });
        List<int[]> mergedRanges = new ArrayList<int[]>();
        int[] currentRange = pos.get(0);
        // 遍历范围列表并合并相邻的范围
        for (int i = 1; i < pos.size(); i++) {
            int[] nextRange = pos.get(i);
            if (currentRange[1] >= nextRange[0]) {
                // 合并范围
                currentRange[1] = Math.max(currentRange[1], nextRange[1]);
            } else {
                // 将当前范围添加到合并后的范围列表中，并更新当前范围为下一个范围
                mergedRanges.add(currentRange);
                currentRange = nextRange;
            }
        }
        // 将最后一个范围添加到合并后的范围列表中
        mergedRanges.add(currentRange);
        return mergedRanges;
    }

    // 合并段落：有索引
    public static void mergeParas(Paragraph paragraph, List<int[]> mergedRanges, Document doc, String paragraphText, int vflag) {
        // 绘制类型
        Map<String, Map<String, Object>> dto = drawRunType(vflag);
        Map<String, Object> isNotBold = dto.get("isNotBold");
        Map<String, Object> isBold = dto.get("isBold");

        // 清空runs
        paragraph.getRuns().clear();

        // 是否索引大小为1，如果为1，代表样式只修改一处；如果大于1，代表样式修改多处
        if (mergedRanges.size() == 1) {
            // 样式只修改一处
            // 初始参数
            int[] range = mergedRanges.get(0);
            int startIndex = range[0];
            int endIndex = range[1];

            // 拼接段落
            // ppt：paragraphText无换行符
            // word：paragraphText.length()-1 去除换行符，paragraphText.length() 保留换行符
            Run beforeRun = new Run(doc, paragraphText.substring(0, startIndex));
            drawRunStyle(beforeRun, isNotBold);
            paragraph.appendChild(beforeRun);
            //
            Run keywordRun = new Run(doc, paragraphText.substring(startIndex, endIndex + 1));
            drawRunStyle(keywordRun, isBold);
            paragraph.appendChild(keywordRun);
            //
            Run afterRun = new Run(doc, paragraphText.substring(endIndex + 1, paragraphText.length()-1));
            drawRunStyle(afterRun, isNotBold);
            paragraph.appendChild(afterRun);
        } else {
            // 样式修改多处
            for (int i = 0; i < mergedRanges.size(); i++) {
                // 初始参数
                int[] range = mergedRanges.get(i);
                int[] range2 = i == mergedRanges.size() - 1 ? mergedRanges.get(i) : mergedRanges.get(i + 1);
                int startIndex = range[0];
                int endIndex = range[1];
                int beforeIndex = range2[0];

                // 拼接段落
                // ppt：paragraphText无换行符
                // word：paragraphText.length()-1 去除换行符，paragraphText.length() 保留换行符
                if (i == 0) {
                    Run beforeRun = new Run(doc, paragraphText.substring(0, startIndex));
                    drawRunStyle(beforeRun, isNotBold);
                    paragraph.appendChild(beforeRun);
                    //
                    Run keywordRun = new Run(doc, paragraphText.substring(startIndex, endIndex + 1));
                    drawRunStyle(keywordRun, isBold);
                    paragraph.appendChild(keywordRun);
                    //
                    Run afterRun = new Run(doc, paragraphText.substring(endIndex + 1, beforeIndex));
                    drawRunStyle(afterRun, isNotBold);
                    paragraph.appendChild(afterRun);
                } else if (i == mergedRanges.size() - 1) {
                    Run keywordRun = new Run(doc, paragraphText.substring(startIndex, endIndex + 1));
                    drawRunStyle(keywordRun, isBold);
                    paragraph.appendChild(keywordRun);
                    //
                    Run afterRun = new Run(doc, paragraphText.substring(endIndex + 1, paragraphText.length()-1));
                    drawRunStyle(afterRun, isNotBold);
                    paragraph.appendChild(afterRun);
                } else {
                    Run keywordRun = new Run(doc, paragraphText.substring(startIndex, endIndex + 1));
                    drawRunStyle(keywordRun, isBold);
                    paragraph.appendChild(keywordRun);
                    //
                    Run afterRun = new Run(doc, paragraphText.substring(endIndex + 1, beforeIndex));
                    drawRunStyle(afterRun, isNotBold);
                    paragraph.appendChild(afterRun);
                }
            }
        }
    }

    // 合并段落：无索引
    public static void mergeParas(Paragraph paragraph, Document doc, String paragraphText, int vflag) {
        // 绘制类型
        Map<String, Map<String, Object>> dto = drawRunType(vflag);
        Map<String, Object> isNotBold = dto.get("isNotBold");
        Map<String, Object> isBold = dto.get("isBold");

        // 清空runs
        paragraph.getRuns().clear();

        // 拼接段落
        Run run = new Run(doc, paragraphText.substring(0, paragraphText.length() -1));
        drawRunStyle(run, isNotBold);
        paragraph.appendChild(run);
    }

    // 绘制类型
    public static Map<String, Map<String, Object>> drawRunType(int vflag) {
        Map<String, Map<String, Object>> result = new HashMap<String, Map<String, Object>>();
        String isNotBold = "s" + vflag + "1";
        String isBold = "s" + vflag + "2";
        result.put("isNotBold", drawRuns.get(isNotBold));
        result.put("isBold", drawRuns.get(isBold));
        return result;
    }

    // 绘制样式
    public static void drawRunStyle(Run run, Map<String, Object> map) {
        Font font = run.getFont();
        try {
            font.setBold((Boolean) map.get("bold"));
            font.setSize((Double) map.get("size"));
            font.setColor((Color) map.get("color"));
            font.setName((String) map.get("name"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
