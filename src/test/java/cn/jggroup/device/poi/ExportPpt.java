package cn.jggroup.device.poi;

import cn.jggroup.device.uitls.FactoryUtils;
import com.sxoms.utils.MwUtils;
import java.awt.Color;
import java.awt.geom.Rectangle2D;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.apache.poi.sl.usermodel.TextParagraph.TextAlign;
import org.apache.poi.sl.usermodel.TableCell.BorderEdge;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFAutoShape;
import org.apache.poi.xslf.usermodel.XSLFPictureData;
import org.apache.poi.xslf.usermodel.XSLFPictureShape;
import org.apache.poi.xslf.usermodel.XSLFShape;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.apache.poi.xslf.usermodel.XSLFTable;
import org.apache.poi.xslf.usermodel.XSLFTableCell;
import org.apache.poi.xslf.usermodel.XSLFTableRow;
import org.apache.poi.xslf.usermodel.XSLFTextParagraph;
import org.apache.poi.xslf.usermodel.XSLFTextRun;

public class ExportPpt {
    // true.访问真实库 false.访问测试库
    private static final boolean dbFlag = true;
    private static MwUtils mwUtils = new MwUtils(dbFlag);
    public static Map<String, String[]> tables = new HashMap<String, String[]>();
    public static Map<String, String[]> colzhs = new HashMap<String, String[]>();
    public static Map<String, String[]> colens = new HashMap<String, String[]>();
    public static Map<String, String[]> colxhs = new HashMap<String, String[]>();
    public static Map<String, String[]> highlights = new HashMap<String, String[]>();
    public static Map<String, Map<String, Object>> drawRunAligns = new HashMap<String, Map<String, Object>>();
    public static Map<String, Map<String, Object>> drawRunStyles = new HashMap<String, Map<String, Object>>();
    public static Map<String, String> rows = new HashMap<String, String>();
    private static String tplFile = getBasePathBy() + "module2.pptx";
    private static String imgPath = getBasePathBy();
    private static final String FILE_SUFFIX = ".png";

    static {
        // 处室
        tables.put("T1", new String[]{"调控处", "MW_APP.NR_OMS_ZPTH_DKCTB"});
        tables.put("T2", new String[]{"计划处", "MW_APP.NR_OMS_ZPTH_JHCTB"});
        tables.put("T3", new String[]{"系统处", "MW_APP.NR_OMS_ZPTH_XTCTB"});
        tables.put("T4", new String[]{"保护处", "MW_APP.NR_OMS_ZPTH_BHCTB"});
        tables.put("T5", new String[]{"自动化处", "MW_APP.NR_OMS_ZPTH_ZDHCTB"});
        tables.put("T6", new String[]{"通信处", "MW_APP.NR_OMS_ZPTH_TXCTB"});
        tables.put("T7", new String[]{"网安处", "MW_APP.NR_OMS_ZPTH_WACTB"});
        tables.put("T8", new String[]{"现货处", "MW_APP.NR_OMS_ZPTH_XHCTB"});
        tables.put("T9", new String[]{"综合处", "MW_APP.NR_OMS_ZPTH_ZHCTB"});
        tables.put("T10", new String[]{"水新处", "MW_APP.NR_OMS_ZPTH_SXCTB"});
        // 图表
        tables.put("T11", new String[]{"本周检修计划安排1", "MW_APP.NR_OMS_ZPTH_JHCTB_T1"});
        tables.put("T12", new String[]{"检修计划开展情况2", "MW_APP.NR_OMS_ZPTH_JHCTB_T2"});
        tables.put("T13", new String[]{"检修计划开展情况3", "MW_APP.NR_OMS_ZPTH_JHCTB_T3"});
        tables.put("T14", new String[]{"二次作业风险表", "MW_APP.NR_OMS_ZPTH_GCTB_ZYFX"});

        // 填报项-中文名
        colzhs.put("T1", new String[]{"电网运行情况", "电网运行情况-重点工作", "上周山西电网设备停运情况统计", "上周省网最大负荷"});
        colzhs.put("T2", new String[]{"电网运行情况", "电力平衡方面", "本周检修计划安排", "月检修计划安排", "重点工作", "上周山西电网检修计划执行情况统计", "本周山西电网检修计划"});
        colzhs.put("T3", new String[]{"电网运行情况", "本周检修计划安排", "重点工作", "新设备安排情况统计"});
        colzhs.put("T4", new String[]{"二次作业风险", "重点工作"});
        colzhs.put("T5", new String[]{"二次作业风险", "重点工作"});
        colzhs.put("T6", new String[]{"二次作业风险", "重点工作"});
        colzhs.put("T7", new String[]{"二次作业风险", "重点工作"});
        colzhs.put("T8", new String[]{"重点工作"});
        colzhs.put("T9", new String[]{"重点工作"});
        colzhs.put("T10", new String[]{"重点工作"});

        // 填报项-英文名
        colens.put("T1", new String[]{"dkc_dwyxqk", "dkc_dwyxqk_zdgz", "dkc_szsxdwsbtyqktj", "dkc_szswzdfh"});
        colens.put("T2", new String[]{"jhc_dwyxqk", "jhc_dlphfb", "jhc_bzjxjhap", "jhc_jxjhap", "jhc_zdgz", "jhc_szsxdwjxjhzxqktj", "jhc_bzsxdwjxjh"});
        colens.put("T3", new String[]{"xtc_dwyxqk", "xtc_bzjxjhap", "xtc_zdgz", "xtc_xsbapqktj"});
        colens.put("T4", new String[]{"bhc_eczyfx", "bhc_zdgz"});
        colens.put("T5", new String[]{"zdhc_eczyfx", "zdhc_zdgz"});
        colens.put("T6", new String[]{"txc_eczyfx", "txc_zdgz"});
        colens.put("T7", new String[]{"wac_eczyfx", "wac_zdgz"});
        colens.put("T8", new String[]{"xhc_zdgz"});
        colens.put("T9", new String[]{"zhc_zdgz"});
        colens.put("T10", new String[]{"sxc_zdgz"});

        // 龘, 骉, 魑, 魍：分别对应4种样式
        // 龘：填报项，不带序号
        // 骉：填报项-重点工作，带序号
        colxhs.put("vf", new String[]{"龘", "骉", "魑", "魍"});
        colxhs.put("xh", new String[]{"1.", "2.", "3.", "4.", "5.", "6.", "7.", "8.", "9.", "10."});
        colxhs.put("text", new String[]{"dkc_dwyxqk_zdgz", "jhc_zdgz", "xtc_zdgz", "bhc_zdgz", "zdhc_zdgz", "txc_zdgz", "wac_zdgz", "xhc_zdgz", "zhc_zdgz", "sxc_zdgz"});

        // 段落对齐方式
        drawRunAligns.put("center", new HashMap<String, Object>() {{ put("bulletfont", "微软雅黑");put("textalign", TextAlign.CENTER);put("bullet", false);}});
        drawRunAligns.put("left", new HashMap<String, Object>() {{ put("bulletfont", "微软雅黑");put("textalign", TextAlign.LEFT);put("bullet", false);}});
        // 构建文本：填充样式
        drawRunStyles.put("title", new HashMap<String, Object>() {{ put("fontfamily", "宋体");put("fontsize", 40.0D); put("bold", true); put("fontcolor", Color.yellow); }});
        drawRunStyles.put("vMonth", new HashMap<String, Object>() {{ put("fontfamily", "微软雅黑");put("fontsize", 32.0D); put("bold", true); put("fontcolor", Color.yellow); }});
        drawRunStyles.put("text", new HashMap<String, Object>() {{ put("fontfamily", "微软雅黑");put("fontsize", 30.0D); put("bold", false); put("fontcolor", Color.yellow); }});
        // 构建样式：渲染样式
        drawRunStyles.put("normal", new HashMap<String, Object>() {{ put("fontfamily", "微软雅黑");put("fontsize", 30.0D); put("bold", false); put("fontcolor", Color.yellow); }});
        drawRunStyles.put("high", new HashMap<String, Object>() {{ put("fontfamily", "微软雅黑");put("fontsize", 30.0D); put("bold", true); put("fontcolor", new Color(252, 152, 4)); }});

        // 加粗关键字
        highlights.put("keywords", new String[]{"省网用电", "公司外送", "雁淮直流", "超高压变电", "重点生产工作", "重点基建工作", "建设分公司", "输电", "各地调", "太原", "大同", "阳泉", "长治", "晋城", "朔州", "晋中", "运城", "忻州", "临汾", "吕梁", "本周检修计划安排1表格中的电网风险等级和备注下填写的内容", "二次作业风险表中的作业风险等级和备注下填写的内容"});
    }

    /**
     * 打印消息
     */
    public static void print(Object msg) {
        System.out.println(msg);
    }

    /**
     * 打印消息
     */
    public static void error(String msg) {
        System.err.println(msg);
    }

    /**
     * 路径
     */
    private static String getBasePathBy() {
        String basePath = "D:/logs/zlh/";
        String os = System.getenv("os");
        if (!("Windows_NT".equals(os))) {
            basePath = "/home/<USER>/Portal/PROJECT-HOME/business/sxd5000/zhjs/zlh/out/";
        }
        return basePath;
    }

    /**
     * GET请求参数
     */
    public static Map<String, Object> getDto(HttpServletRequest request) {
        Map<String, Object> map = new HashMap<String, Object>();
        Enumeration keys = request.getParameterNames();
        while (keys.hasMoreElements()) {
            String key = (String) keys.nextElement();
            String val = request.getParameter(key);
            if (key != null && !"".equals(key)) {
                map.put(key, val);
            }
        }
        return map;
    }

    /******************************************************************************************************************/

    public static void main(String[] args) {
        try {
            print("模板：" + tplFile);
            String rq = "2024-05-10";
            XMLSlideShow ppt = export(rq);
            String path = getBasePathBy() + "周例会工作汇报_" + FactoryUtils.getDate(rq, "yyyyMMdd") + ".pptx";
            ppt.write(new FileOutputStream(path));
            print("导出：" + path);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /******************************************************************************************************************/

    private static XMLSlideShow export(String rq) {
        // 获取数据
        Map<String, String> map = query(rq);
        rows = buildTextSplit(map);
        rows.put("rq", rq);
        rows.put("cs", "调控中心");
        rows.put("vMonth", "       " + "4." + new SimpleDateFormat("MM").format(FactoryUtils.getDate2(rq)).replaceFirst("^0*", "") + "月检修计划安排");

        InputStream is = null;
        XMLSlideShow slideShow = null;
        try {
            // 构建文本、表格、图片
            is = new FileInputStream(tplFile);
            slideShow = new XMLSlideShow(is);
            // 构建文本
            changeTxt(slideShow);
            // 构建样式
            buildStyle(slideShow);
            // 构建表格
            changeTab(slideShow, queryTab(rq));
            // 构建文本图片
            changePic(slideShow, rq);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return slideShow;
    }

    /******************************************************************************************************************/

    // 查询T1-T10
    public static Map<String, String> query(String rq) {
        Map<String, String> result = new HashMap<String, String>();
        for (int j = 1; j <= 10; j++) {
            String tabName = tables.get("T" + j)[1];
            String[] colzhIdx = colzhs.get("T" + j);
            String[] colenIdx = colens.get("T" + j);
            for (int k = 0; k < colzhIdx.length; k++) {
                String sql = "SELECT NR FROM " + tabName + " WHERE LX = '" + colzhIdx[k] + "' AND RQ = '" + rq + "'";
                // print("SQL：" + sql);
                List<Map<String, Object>> ls = mwUtils.queryList(sql);
                String rs = "";
                if (ls.size() > 0) {
                    rs = (String) ls.get(0).get("nr");
                }
                result.put(colenIdx[k], rs);
            }
        }
        return result;
    }

    // 查询T11、T14
    public static List<LinkedHashMap<String, Object>> queryTab(String rq) {
        List<LinkedHashMap<String, Object>> result = new ArrayList<LinkedHashMap<String, Object>>();
        // T11
        LinkedHashMap<String, Object> dto1 = new LinkedHashMap<String, Object>();
        String[] cols1 = {"xh", "dw", "jxsb", "jxsj", "jxsj", "bz"};
        String sql1 = "SELECT * FROM MW_APP.NR_OMS_ZPTH_JHCTB_T1 WHERE RQ = '" + rq + "'";
        List<Map<String, Object>> data1 = mwUtils.queryList(sql1);
        dto1.put("data", data1);
        dto1.put("cols", cols1);
        result.add(dto1);
        // T14
        LinkedHashMap<String, Object> dto2 = new LinkedHashMap<String, Object>();
        String[] cols2 = {"xh", "dw", "ztxmmc", "zysj", "zyfxdj", "bz"};
        String sql2 = "SELECT * FROM MW_APP.NR_OMS_ZPTH_GCTB_ZYFX WHERE RQ = '" + rq + "'";
        List<Map<String, Object>> data2 = mwUtils.queryList(sql2);
        for (int i = 0; i < data2.size(); i++) {
            Map<String, Object> map = data2.get(i);
            map.put("xh", String.valueOf(i + 1));
        }
        dto2.put("data", data2);
        dto2.put("cols", cols2);
        result.add(dto2);
        return result;
    }

    // 处理文字前缀，用于标记处理的特殊段落
    // 填报项，不带序号；拼接为 【前缀（龘）+文本】 + 【\n】 + 【前缀（龘）+文本】
    // 填报项-重点工作，带序号，依次排序为1、2、3、4、5、6、7、8、9、10；拼接为 【前缀（骉）＋序号+文本】 + 【\n】 + 【前缀（魑）+文本】
    public static Map<String, String> buildTextSplit(Map<String, String> result) {
        Map<String, String> rs = new HashMap<String, String>();
        Object[] keys = result.keySet().toArray();
        for (int i = 0; i < keys.length; i++) {
            String key = (String) keys[i];
            String value = result.get(key);
            if (value != null) {
                String[] vf = colxhs.get("vf");
                String[] xhs = colxhs.get("xh");
                String[] text = colxhs.get("text");
                String[] split = value.split("\r|\n");
                int index = -1;
                for (int j = 0; j < text.length; j++) {
                    if (text[j].equals(key)) {
                        index = j;
                        break;
                    }
                }
                if (index > -1) {
                    for (int j = 0; j < split.length; j++) {
                        if (j == 0) {
                            value = vf[1] + "       " + xhs[index] + split[0];
                        } else {
                            value = value + "\n" + "       " + split[j];
                        }
                    }
                } else {
                    for (int j = 0; j < split.length; j++) {
                        if (j == 0) {
                            value = vf[0] + "       " + split[0];
                        } else {
                            value = value + "\n" + "       " + split[j];
                        }
                    }
                }
            }
            rs.put(key, value);
        }
        return rs;
    }

    /******************************************************************************************************************/

    // 第一步：构建文本
    private static void changeTxt(XMLSlideShow slideShow) {
        List<XSLFSlide> slidesList = slideShow.getSlides();
        for (XSLFSlide slide : slidesList) {
            List<XSLFShape> shapesList = slide.getShapes();
            for (XSLFShape shape : shapesList) {
                if (shape instanceof XSLFAutoShape) {
                    XSLFAutoShape autoShape = (XSLFAutoShape) shape;
                    String paragraphText = autoShape.getText();
                    XSLFTextParagraph n = null;
                    XSLFTextRun run = null;
                    // 段落对齐方式
                    Map<String, Object> center = drawRunAligns.get("center");
                    Map<String, Object> left = drawRunAligns.get("left");
                    // 构建样式：渲染样式
                    Map<String, Object> title = drawRunStyles.get("title");
                    Map<String, Object> vMonth = drawRunStyles.get("vMonth");
                    Map<String, Object> text = drawRunStyles.get("text");
                    // 当前段落 匹配 rows的Key
                    Object[] keys = rows.keySet().toArray();
                    for (int i = 0; i < rows.size(); i++) {
                        String key = (String) keys[i];
                        String value = rows.get(key);
                        if (paragraphText.contains("{" + key + "}")) {
                            if ("cs".equals(key) || "rq".equals(key)) {
                                drawRunPara(drawRunClear(autoShape, n, center), run, value, title);
                            } else if ("vMonth".equals(key)) {
                                drawRunPara(drawRunClear(autoShape, n, left), run, value, vMonth);
                            } else {
                                drawRunPara(drawRunClear(autoShape, n, left), run, value, text);
                            }
                        }
                    }
                }
            }
        }
    }

    /******************************************************************************************************************/

    // 第二步：构建表格
    private static void changeTab(XMLSlideShow slideShow, List<LinkedHashMap<String, Object>> list) {
        // 获取每个List数据
        // 说明：因为循环渲染，发现表格重叠，因未搞清楚重叠原因，故这里没有循环渲染表格
        List<Map<String, Object>> dt1 = (List<Map<String, Object>>) list.get(0).get("data");
        String[] cols1 = (String[]) list.get(0).get("cols");
        List<Map<String, Object>> dt2 = (List<Map<String, Object>>) list.get(1).get("data");
        String[] cols2 = (String[]) list.get(1).get("cols");

        //判别是第几张表格
        int fCount = 0;
        List<XSLFSlide> slidesList = slideShow.getSlides();
        for (XSLFSlide slide : slidesList) {
            XSLFShape[] shapes = slide.getShapes();
            for (XSLFShape shape : shapes) {
                if (shape instanceof XSLFTable) {
                    XSLFTable table = (XSLFTable) shape;
                    if (fCount == 0) {
                        fCount++;
                        int totalRows = dt1.size();
                        int maxRowsPerPage = 8;
                        int currentPageRows = Math.min(totalRows,maxRowsPerPage);
                        // 获取表格所在页的高度
//                        double totalHeight = slide.getSlideShow().getPageSize().getHeight();
                        // 设置表格所在页的高度
                        double totalHeight = 400.0;
                        double rowHeigt = totalHeight / maxRowsPerPage;
                        for (int rowIndex = 0;rowIndex<currentPageRows;rowIndex++) {
                            Map<String, Object> rowData = dt1.get(rowIndex);
                            XSLFTableRow tableRow = table.addRow();
                            tableRow.setHeight(rowHeigt);
                            for (int j = 0; j < cols1.length; j++) {
                                XSLFTableCell cell = tableRow.addCell();
                                XSLFTextParagraph paragraph = cell.addNewTextParagraph();
                                //设置文本居中
                                paragraph.setTextAlign(TextAlign.CENTER);
                                paragraph.setBulletFont("微软雅黑");
                                paragraph.setBullet(false);
                                XSLFTextRun run = paragraph.addNewTextRun();
                                run.setText(FactoryUtils.objToStr(rowData.get(cols1[j])));
                                run.setFontSize(12.0D);
                                tableStyle(cell);
                            }
                        }
                    } else if (fCount == 1) {
                        fCount++;
                        int totalRows = dt2.size();
                        int maxRowsPerPage = 10;
                        int currentPageRows = Math.min(totalRows,maxRowsPerPage);
                        //设置表格所在页的高度
                        double totalHeight = 400.0;
                        double rowHeigt = totalHeight / maxRowsPerPage;
                        for (int rowIndex = 0;rowIndex<currentPageRows;rowIndex++) {
                            Map<String, Object> rowData = dt2.get(rowIndex);
                            XSLFTableRow tableRow = table.addRow();
                            tableRow.setHeight(rowHeigt);
                            for (int j = 0; j < cols2.length; j++) {
                                XSLFTableCell cell = tableRow.addCell();
                                XSLFTextParagraph paragraph = cell.addNewTextParagraph();
                                //设置文本居中
                                paragraph.setTextAlign(TextAlign.CENTER);
                                paragraph.setBulletFont("微软雅黑");
                                paragraph.setBullet(false);
                                XSLFTextRun run = paragraph.addNewTextRun();
                                run.setText(FactoryUtils.objToStr(rowData.get(cols2[j])));
                                run.setFontSize(12.0D);
                                tableStyle(cell);
                            }
                        }
                    }
                }
            }
        }
    }

    // 表格添加框线及框线颜色
    public static void tableStyle(XSLFTableCell cell) {
        cell.setBorderColor(BorderEdge.bottom, Color.black);
        cell.setBorderColor(BorderEdge.left, Color.black);
        cell.setBorderColor(BorderEdge.top, Color.black);
        cell.setBorderColor(BorderEdge.right, Color.black);
        cell.setBorderWidth(BorderEdge.bottom, 1.0);
        cell.setBorderWidth(BorderEdge.left, 1.0);
        cell.setBorderWidth(BorderEdge.top, 1.0);
        cell.setBorderWidth(BorderEdge.right, 1.0);
        cell.setFillColor(Color.white);
    }

    /******************************************************************************************************************/

    // 第三步：构建图片
    private static void changePic(XMLSlideShow slideShow, String rq) {
        try {
            List<XSLFSlide> slidesList = slideShow.getSlides();
            for (XSLFSlide slide : slidesList) {
                XSLFShape[] shapes = slide.getShapes();
                for (XSLFShape shape : shapes) {
                    Rectangle2D anchor = shape.getAnchor();
                    if (shape instanceof XSLFPictureShape) {
                        File imageFile = new File(imgPath + FactoryUtils.getDate(rq, "yyyyMMdd") + FILE_SUFFIX);
                        if (imageFile.exists()) {
                            FileInputStream fis = new FileInputStream(imageFile);
                            byte[] newImageBytes = new byte[(int) imageFile.length()];
                            fis.read(newImageBytes);
                            fis.close();
                            int pictureType = slideShow.addPicture(newImageBytes, XSLFPictureData.PICTURE_TYPE_PNG);
                            XSLFPictureShape pic = slide.createPicture(pictureType);
                            pic.setAnchor(anchor);
                        }
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /******************************************************************************************************************/

    // 第四步：构建样式
    private static void buildStyle(XMLSlideShow slideShow) {
        // 遍历文档中的每个段落
        List<XSLFSlide> slidesList = slideShow.getSlides();
        for (XSLFSlide slide : slidesList) {
            XSLFShape[] shapes = slide.getShapes();
            for (XSLFShape shape : shapes) {
                if (shape instanceof XSLFAutoShape) {
                    XSLFAutoShape autoShape = (XSLFAutoShape) shape;
                    String paragraphText = autoShape.getText();
                    XSLFTextParagraph n = null;
                    XSLFTextRun run = null;

                    // 判断样式类型
                    int vflag = isType(paragraphText);
//                    print(paragraphText);
//                    print(vflag);
//                    print("");

                    // 处理各种样式
                    if (vflag > -1) {
                        // 将"龘", "骉", "魑", "魍"标志文字删除
                        paragraphText = paragraphText.substring(1);
                        // 循环当前段落中的全部关键字，将索引存储起来段落
                        List<int[]> pos = findBoldIndex(paragraphText, vflag);
                        if (pos.size() > 0) {
                            // 合并重复索引
                            List<int[]> mergedRanges = mergeRanges(pos);
                            // 合并段落：有索引
                            mergeParas(autoShape, n, run, mergedRanges, paragraphText);
                        } else {
                            // 合并段落：无索引
                            mergeParas(autoShape, n, run, paragraphText, vflag);
                        }
                    }
                }
            }
        }
    }

    // 判断样式类型
    public static int isType(String paragraphText) {
        String[] vf = colxhs.get("vf");
        int index = -1;
        for (int j = 0; j < vf.length; j++) {
            if (!"".equals(paragraphText)) {
                if (vf[j].equals(paragraphText.substring(0, 1))) {
                    index = j;
                    break;
                }
            }
        }
        return index;
    }

    // 循环当前段落中的全部关键字，将索引存储起来段落
    public static List<int[]> findBoldIndex(String paragraphText, int vflag) {
        List<int[]> pos = new ArrayList<int[]>();
        // 第一个句号前的文字加粗
        // 骉：填报项-重点工作，带序号
        if (vflag == 1) {
            int index = paragraphText.indexOf("。");
            if (index > 0) {
                pos.add(new int[]{0, index});
            }
        }
        // 关键字加粗
        for (String keyword : highlights.get("keywords")) {
            if (paragraphText.contains(keyword)) {
                int index = paragraphText.indexOf(keyword);
                while (index >= 0) {
                    pos.add(new int[]{index, index + keyword.length() - 1});
                    index = paragraphText.indexOf(keyword, index + 1);
                }
            }
        }
        return pos;
    }

    // 合并重复索引
    public static List<int[]> mergeRanges(List<int[]> pos) {
        // 如果范围为空，直接返回空列表
        if (pos.isEmpty()) {
            return new ArrayList<int[]>();
        }
        // 对pos按照第一个元素的大小进行排序
        Collections.sort(pos, new Comparator<int[]>() {
            public int compare(int[] a, int[] b) {
                if (a[0] < b[0]) {
                    return -1;
                } else if (a[0] > b[0]) {
                    return 1;
                } else {
                    return 0;
                }
            }
        });
        List<int[]> mergedRanges = new ArrayList<int[]>();
        int[] currentRange = pos.get(0);
        // 遍历范围列表并合并相邻的范围
        for (int i = 1; i < pos.size(); i++) {
            int[] nextRange = pos.get(i);
            if (currentRange[1] >= nextRange[0]) {
                // 合并范围
                currentRange[1] = Math.max(currentRange[1], nextRange[1]);
            } else {
                // 将当前范围添加到合并后的范围列表中，并更新当前范围为下一个范围
                mergedRanges.add(currentRange);
                currentRange = nextRange;
            }
        }
        // 将最后一个范围添加到合并后的范围列表中
        mergedRanges.add(currentRange);
        return mergedRanges;
    }

    // 合并段落：有索引
    public static void mergeParas(XSLFAutoShape autoShape, XSLFTextParagraph n, XSLFTextRun run, List<int[]> mergedRanges, String paragraphText) {
        // 段落对齐方式
        Map<String, Object> left = drawRunAligns.get("left");
        // 构建样式：渲染样式
        Map<String, Object> normal = drawRunStyles.get("normal");
        Map<String, Object> high = drawRunStyles.get("high");

        // 清空runs
        n = drawRunClear(autoShape, n, left);

        // 是否索引大小为1，如果为1，代表样式只修改一处；如果大于1，代表样式修改多处
        if (mergedRanges.size() == 1) {
            // 样式只修改一处
            // 初始参数
            int[] range = mergedRanges.get(0);
            int startIndex = range[0];
            int endIndex = range[1];

            // 拼接段落
            // ppt：paragraphText无换行符
            // word：paragraphText.length()-1 去除换行符，paragraphText.length() 保留换行符
            String beforeRun = paragraphText.substring(0, startIndex);
            String keywordRun = paragraphText.substring(startIndex, endIndex + 1);
            String afterRun = paragraphText.substring(endIndex + 1, paragraphText.length());
            drawRunPara(n, run, beforeRun, normal);
            drawRunPara(n, run, keywordRun, high);
            drawRunPara(n, run, afterRun, normal);
        } else {
            // 样式修改多处
            for (int i = 0; i < mergedRanges.size(); i++) {
                // 初始参数
                int[] range = mergedRanges.get(i);
                int[] range2 = i == mergedRanges.size() - 1 ? mergedRanges.get(i) : mergedRanges.get(i + 1);
                int startIndex = range[0];
                int endIndex = range[1];
                int beforeIndex = range2[0];

                // 拼接段落
                // ppt：paragraphText无换行符
                // word：paragraphText.length()-1 去除换行符，paragraphText.length() 保留换行符
                if (i == 0) {
                    String beforeRun = paragraphText.substring(0, startIndex);
                    String keywordRun = paragraphText.substring(startIndex, endIndex + 1);
                    String afterRun = paragraphText.substring(endIndex + 1, beforeIndex);
                    drawRunPara(n, run, beforeRun, normal);
                    drawRunPara(n, run, keywordRun, high);
                    drawRunPara(n, run, afterRun, normal);
                } else if (i == mergedRanges.size() - 1) {
                    String keywordRun = paragraphText.substring(startIndex, endIndex + 1);
                    String afterRun = paragraphText.substring(endIndex + 1, paragraphText.length());
                    drawRunPara(n, run, keywordRun, high);
                    drawRunPara(n, run, afterRun, normal);
                } else {
                    String keywordRun = paragraphText.substring(startIndex, endIndex + 1);
                    String afterRun = paragraphText.substring(endIndex + 1, beforeIndex);
                    drawRunPara(n, run, keywordRun, high);
                    drawRunPara(n, run, afterRun, normal);
                }
            }
        }
    }

    // 合并段落：无索引
    public static void mergeParas(XSLFAutoShape autoShape, XSLFTextParagraph n, XSLFTextRun run, String paragraphText, int vflag) {
        // 段落对齐方式
        Map<String, Object> left = drawRunAligns.get("left");
        // 构建样式：渲染样式
        Map<String, Object> style = drawRunStyles.get("normal");
        if (vflag == 0) {
            style = drawRunStyles.get("normal");
        } else if (vflag == 1) {
            style = drawRunStyles.get("high");
        }

        // 清空runs
        n = drawRunClear(autoShape, n, left);
        String text = paragraphText;
        drawRunPara(n, run, text, style);
    }

    // 设置对齐，清空runs
    public static XSLFTextParagraph drawRunClear(XSLFAutoShape autoShape, XSLFTextParagraph n, Map<String, Object> map) {
        autoShape.clearText();
        n = autoShape.addNewTextParagraph();
        n.setBulletFont((String) map.get("bulletfont"));
        n.setTextAlign((TextAlign) map.get("textalign"));
        n.setBullet((Boolean) map.get("bullet"));
        return n;
    }

    // 绘制段落，拼接段落
    public static void drawRunPara(XSLFTextParagraph n, XSLFTextRun run, String value, Map<String, Object> map) {
        run = n.addNewTextRun();
        run.setText(value);
        run.setFontFamily((String) map.get("fontfamily"));
        run.setFontSize((Double) map.get("fontsize"));
        run.setBold((Boolean) map.get("bold"));
        run.setFontColor((Color) map.get("fontcolor"));
    }
}
