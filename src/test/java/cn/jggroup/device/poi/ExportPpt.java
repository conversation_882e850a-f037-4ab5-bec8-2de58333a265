package cn.jggroup.device.poi;

import cn.jggroup.device.uitls.FactoryUtils;
import java.awt.Color;
import java.awt.geom.Rectangle2D;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.poi.sl.usermodel.PictureData;
import org.apache.poi.sl.usermodel.TableCell.BorderEdge;
import org.apache.poi.sl.usermodel.TextParagraph.TextAlign;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFAutoShape;
import org.apache.poi.xslf.usermodel.XSLFPictureData;
import org.apache.poi.xslf.usermodel.XSLFPictureShape;
import org.apache.poi.xslf.usermodel.XSLFShape;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.apache.poi.xslf.usermodel.XSLFTable;
import org.apache.poi.xslf.usermodel.XSLFTableCell;
import org.apache.poi.xslf.usermodel.XSLFTableRow;
import org.apache.poi.xslf.usermodel.XSLFTextParagraph;
import org.apache.poi.xslf.usermodel.XSLFTextRun;

/**
 * 功能概述：
 * 基于Apache POI 4.1.2生成电力系统周例会工作汇报PPT
 * 支持文本替换、表格生成、图片替换、样式渲染等功能
 *
 * 主要处理流程：
 * 1.数据准备：模拟各处室工作数据（替代数据库查询）
 * 2.文本处理：处理占位符替换和样式标记
 * 3.表格生成：动态生成检修计划和风险作业表格
 * 4.图片替换：替换模板中的图片占位符
 * 5.样式应用：应用字体、颜色、对齐等样式效果
 */
public class ExportPpt {
    // ================================ 数据配置映射区域 ================================

    // 填报项英文名映射：各处室(T1-T10)对应的英文字段名数组
    public static Map<String, String[]> colens = new HashMap<String, String[]>();
    // 特殊标记配置：用于文本样式处理的标记符号(龘骉魑魍)和序号配置
    public static Map<String, String[]> colxhs = new HashMap<String, String[]>();
    // 关键词高亮配置：需要在文本中加粗显示的关键词列表
    public static Map<String, String[]> highlights = new HashMap<String, String[]>();
    // 段落对齐方式配置：包含居中、左对齐等文本对齐样式设置
    public static Map<String, Map<String, Object>> drawRunAligns = new HashMap<String, Map<String, Object>>();
    // 文本样式配置：包含字体、大小、颜色、加粗等样式设置
    public static Map<String, Map<String, Object>> drawRunStyles = new HashMap<String, Map<String, Object>>();
    // 处理后的文本数据：经过样式标记处理的最终文本内容，用于PPT生成
    public static Map<String, String> rows = new HashMap<String, String>();

    // ================================ 文件路径配置区域 ================================

    // PPT模板文件完整路径
    private static String tplFile = getTemplatePathBy() + "module2.pptx";
    // 图片文件存放路径
    private static String imgPath = getTemplatePathBy();
    // 图片文件扩展名
    private static final String FILE_SUFFIX = ".png";

    // ================================ 静态初始化配置区域 ================================
    static {
        // -------------------- 各处室英文字段名配置 --------------------
        // T1-调控处：电网运行情况、重点工作、设备停运统计、最大负荷
        colens.put("T1", new String[]{"dkc_dwyxqk", "dkc_dwyxqk_zdgz", "dkc_szsxdwsbtyqktj", "dkc_szswzdfh"});
        // T2-计划处：电网运行、电力平衡、检修计划、重点工作等
        colens.put("T2", new String[]{"jhc_dwyxqk", "jhc_dlphfb", "jhc_bzjxjhap", "jhc_jxjhap", "jhc_zdgz", "jhc_szsxdwjxjhzxqktj", "jhc_bzsxdwjxjh"});
        // T3-系统处：电网运行、检修安排、重点工作、新设备评估
        colens.put("T3", new String[]{"xtc_dwyxqk", "xtc_bzjxjhap", "xtc_zdgz", "xtc_xsbapqktj"});
        // T4-保护处：二次作业风险、重点工作
        colens.put("T4", new String[]{"bhc_eczyfx", "bhc_zdgz"});
        // T5-自动化处：二次作业风险、重点工作
        colens.put("T5", new String[]{"zdhc_eczyfx", "zdhc_zdgz"});
        // T6-通信处：二次作业风险、重点工作
        colens.put("T6", new String[]{"txc_eczyfx", "txc_zdgz"});
        // T7-网安处：二次作业风险、重点工作
        colens.put("T7", new String[]{"wac_eczyfx", "wac_zdgz"});
        // T8-现货处：重点工作
        colens.put("T8", new String[]{"xhc_zdgz"});
        // T9-综合处：重点工作
        colens.put("T9", new String[]{"zhc_zdgz"});
        // T10-水新处：重点工作
        colens.put("T10", new String[]{"sxc_zdgz"});

        // -------------------- 文本样式标记配置 --------------------
        // 龘：普通填报项，不带序号  骉：重点工作项，带序号  魑、魍：其他样式预留
        colxhs.put("vf", new String[]{"龘", "骉", "魑", "魍"});
        // 序号配置：用于重点工作项的自动编号
        colxhs.put("xh", new String[]{"1.", "2.", "3.", "4.", "5.", "6.", "7.", "8.", "9.", "10."});
        // 需要带序号的重点工作字段列表
        colxhs.put("text", new String[]{"dkc_dwyxqk_zdgz", "jhc_zdgz", "xtc_zdgz", "bhc_zdgz", "zdhc_zdgz", "txc_zdgz", "wac_zdgz", "xhc_zdgz", "zhc_zdgz", "sxc_zdgz"});

        // -------------------- 段落对齐方式配置 --------------------
        // 居中对齐：用于标题等重要内容
        drawRunAligns.put("center", new HashMap<String, Object>() {{put("bulletfont", "微软雅黑"); put("textalign", TextAlign.CENTER); put("bullet", false);}});
        // 左对齐：用于正文内容
        drawRunAligns.put("left", new HashMap<String, Object>() {{put("bulletfont", "微软雅黑"); put("textalign", TextAlign.LEFT); put("bullet", false);}});

        // -------------------- 文本样式配置 --------------------
        // 标题样式：宋体、40号、加粗、黄色
        drawRunStyles.put("title", new HashMap<String, Object>() {{put("fontfamily", "宋体"); put("fontsize", 40.0D); put("bold", true); put("fontcolor", Color.yellow);}});
        // 月份标题样式：微软雅黑、32号、加粗、黄色
        drawRunStyles.put("vMonth", new HashMap<String, Object>() {{put("fontfamily", "微软雅黑"); put("fontsize", 32.0D); put("bold", true); put("fontcolor", Color.yellow);}});
        // 普通文本样式：微软雅黑、30号、不加粗、黄色
        drawRunStyles.put("text", new HashMap<String, Object>() {{put("fontfamily", "微软雅黑"); put("fontsize", 30.0D); put("bold", false); put("fontcolor", Color.yellow);}});
        // 正常文本样式：用于段落中的普通文字
        drawRunStyles.put("normal", new HashMap<String, Object>() {{put("fontfamily", "微软雅黑"); put("fontsize", 30.0D); put("bold", false); put("fontcolor", Color.yellow);}});
        // 高亮文本样式：用于关键词加粗显示，橙色
        drawRunStyles.put("high", new HashMap<String, Object>() {{put("fontfamily", "微软雅黑"); put("fontsize", 30.0D); put("bold", true); put("fontcolor", new Color(252, 152, 4));}});

        // -------------------- 关键词高亮配置 --------------------
        // 需要在文本中自动加粗显示的关键词列表
        highlights.put("keywords", new String[]{"省网用电", "公司外送", "雁淮直流", "超高压变电", "重点生产工作", "重点基建工作", "建设分公司", "输电", "各地调", "太原", "大同", "阳泉", "长治", "晋城", "朔州", "晋中", "运城", "忻州", "临汾", "吕梁", "本周检修计划安排1表格中的电网风险等级和备注下填写的内容", "二次作业风险表中的作业风险等级和备注下填写的内容"
        });
    }

    // ================================ 工具方法区域 ================================

    /**
     * 控制台信息输出
     * @param msg 要输出的消息对象
     */
    public static void print(Object msg) {
        System.out.println(msg);
    }

    /**
     * 控制台错误信息输出
     * @param msg 要输出的错误消息
     */
    public static void error(String msg) {
        System.err.println(msg);
    }

    /**
     * 获取PPT模板文件存放路径
     * @return 模板文件目录路径
     */
    private static String getTemplatePathBy() {
        String templatePath = "src/main/resources/office/pptx/";
        return templatePath;
    }

    /**
     * 获取PPT导出文件存放路径
     * @return 导出文件目录路径
     */
    private static String getExportPathBy() {
        String exportPath = "src/test/resources/export/";
        return exportPath;
    }

    // ================================ 主程序入口区域 ================================

    /**
     * 程序主入口
     * 演示PPT生成的完整流程
     * @param args 命令行参数（未使用）
     */
    public static void main(String[] args) {
        try {
            print("使用模板：" + tplFile);
            String rq = "2024-05-10";
            XMLSlideShow ppt = export(rq);
            String path = getExportPathBy() + "ExportPpt" + ".pptx";
            ppt.write(new FileOutputStream(path));
            print("导出完成：" + path);
        } catch (IOException e) {
            error("PPT导出失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    // ================================ 核心导出方法区域 ================================

    /**
     * PPT导出核心方法
     * 整合数据处理、模板加载、内容填充等完整流程
     *
     * @param rq 报告日期，格式：yyyy-MM-dd
     * @return 生成的XMLSlideShow对象
     */
    private static XMLSlideShow export(String rq) {
        // -------------------- 数据准备阶段 --------------------
        print("开始数据准备...");
        Map<String, String> map = query(rq);
        rows = buildTextSplit(map);

        // 添加固定数据项
        rows.put("rq", rq);
        rows.put("cs", "调控中心");
        rows.put("vMonth", "       " + "4." + new SimpleDateFormat("MM").format(FactoryUtils.getDate2(rq)).replaceFirst("^0*", "") + "月检修计划安排");

        // -------------------- PPT生成阶段 --------------------
        InputStream is = null;
        XMLSlideShow slideShow = null;
        try {
            print("加载PPT模板...");
            is = new FileInputStream(tplFile);  // 读取模板文件
            slideShow = new XMLSlideShow(is);  // 创建PPT对象

            print("开始内容填充...");
            changeTxt(slideShow);  // 第一步：填充文本内容
            buildStyle(slideShow);  // 第二步：应用文本样式
            changeTab(slideShow, queryTab(rq));  // 第三步：生成表格数据
            changePic(slideShow, rq);  // 第四步：替换图片内容

            print("内容填充完成");
        } catch (FileNotFoundException e) {
            error("模板文件未找到：" + tplFile);
            e.printStackTrace();
        } catch (IOException e) {
            error("PPT处理过程中发生IO错误：" + e.getMessage());
            e.printStackTrace();
        }
        return slideShow;
    }

    // ================================ 数据模拟区域 ================================

    /**
     * 模拟查询各处室工作数据
     * 替代原有的数据库查询功能，生成测试用的工作汇报数据
     *
     * @param rq 报告日期（格式：yyyy-MM-dd）
     * @return 包含各处室工作内容的键值对映射
     */
    public static Map<String, String> query(String rq) {
        Map<String, String> result = new HashMap<String, String>();

        // -------------------- T1-调控处数据 --------------------
        result.put("dkc_dwyxqk", "本周省网用电负荷平稳运行，最大负荷达到2850万千瓦，同比增长3.2%。\n电网运行总体稳定，未发生重大设备故障。");
        result.put("dkc_dwyxqk_zdgz", "完成春季安全大检查工作部署。\n组织开展电网运行方式优化分析。\n加强负荷预测和电力平衡工作。");
        result.put("dkc_szsxdwsbtyqktj", "500千伏设备停运2项，220千伏设备停运5项，110千伏设备停运8项。\n主要原因为计划检修和设备维护。");
        result.put("dkc_szswzdfh", "上周省网最大负荷出现在4月15日14:30，达到2850万千瓦。\n较去年同期增长3.2%，电力供需总体平衡。");

        // -------------------- T2-计划处数据 --------------------
        result.put("jhc_dwyxqk", "电网运行平稳，各项指标正常。设备健康水平良好，运行可靠性持续提升。");
        result.put("jhc_dlphfb", "本周电力平衡总体良好，火电出力稳定，新能源发电量同比增长15%。\n预计下周用电负荷将有所上升。");
        result.put("jhc_bzjxjhap", "本周安排500千伏检修计划3项，220千伏检修计划6项，110千伏检修计划12项。\n重点关注春季检修安全管控。");
        result.put("jhc_jxjhap", "5月份检修计划安排：500千伏检修15项，220千伏检修28项，110千伏检修45项。\n确保迎峰度夏前完成重要设备检修。");
        result.put("jhc_zdgz", "推进年度检修计划执行。\n加强检修现场安全管控。\n做好迎峰度夏检修准备工作。");
        result.put("jhc_szsxdwjxjhzxqktj", "上周完成检修计划执行率98.5%，其中500千伏完成率100%，220千伏完成率97.8%。");
        result.put("jhc_bzsxdwjxjh", "本周计划检修500千伏线路2条，220千伏线路4条，110千伏线路8条。");

        // -------------------- T3-系统处数据 --------------------
        result.put("xtc_dwyxqk", "电网运行方式合理，潮流分布均匀。系统频率、电压等指标均在正常范围内。");
        result.put("xtc_bzjxjhap", "本周系统检修安排充分考虑电网安全约束，确保N-1安全准则。");
        result.put("xtc_zdgz", "完成夏季运行方式研究。\n开展电网安全稳定分析。\n推进新能源消纳能力提升工作。");
        result.put("xtc_xsbapqktj", "新设备投运前安全评估3项，包括220千伏变电站扩建工程2项，110千伏新建工程1项。");

        // -------------------- T4-保护处数据 --------------------
        result.put("bhc_eczyfx", "二次设备运行稳定，保护装置动作正确率100%。\n重点关注春季雷雨季节保护设备防护。");
        result.put("bhc_zdgz", "开展继电保护定值核查工作。\n完成保护装置春季巡检。\n推进保护设备技术改造。");

        // -------------------- T5-自动化处数据 --------------------
        result.put("zdhc_eczyfx", "自动化系统运行正常，数据采集完整率99.8%。\n调度自动化系统稳定可靠。");
        result.put("zdhc_zdgz", "推进调度自动化系统升级改造。\n完成新投运站点自动化调试。\n加强网络安全防护工作。");

        // -------------------- T6-通信处数据 --------------------
        result.put("txc_eczyfx", "通信网络运行稳定，通信质量良好。\n光纤通信网络可用率99.9%。");
        result.put("txc_zdgz", "推进5G通信技术应用试点。\n完成通信设备春季维护。\n加强通信网络安全管理。");

        // -------------------- T7-网安处数据 --------------------
        result.put("wac_eczyfx", "网络安全态势总体平稳，未发现重大安全威胁。\n安全防护设备运行正常。");
        result.put("wac_zdgz", "开展网络安全专项检查。\n完善网络安全应急预案。\n推进安全防护体系建设。");

        // -------------------- T8-现货处数据 --------------------
        result.put("xhc_zdgz", "推进电力现货市场建设。\n完善市场交易规则。\n加强市场监管和风险防控。");

        // -------------------- T9-综合处数据 --------------------
        result.put("zhc_zdgz", "做好综合协调服务工作。\n推进管理制度完善。\n加强内部管理和监督检查。");

        // -------------------- T10-水新处数据 --------------------
        result.put("sxc_zdgz", "推进新能源项目建设。\n加强水电调度管理。\n做好清洁能源消纳工作。");

        return result;
    }

    // 模拟查询T11、T14表格数据
    public static List<LinkedHashMap<String, Object>> queryTab(String rq) {
        List<LinkedHashMap<String, Object>> result = new ArrayList<LinkedHashMap<String, Object>>();

        // T11 - 本周检修计划安排1
        LinkedHashMap<String, Object> dto1 = new LinkedHashMap<String, Object>();
        String[] cols1 = {"xh", "dw", "jxsb", "jxsj", "jxsj", "bz"};
        List<Map<String, Object>> data1 = new ArrayList<Map<String, Object>>();

        // 生成检修计划数据
        Map<String, Object> row1 = new HashMap<String, Object>();
        row1.put("xh", "1");
        row1.put("dw", "太原供电公司");
        row1.put("jxsb", "500千伏太原变1号主变");
        row1.put("jxsj", "2024-04-20 08:00-18:00");
        row1.put("bz", "年度例行检修，电网风险等级：黄色");
        data1.add(row1);

        Map<String, Object> row2 = new HashMap<String, Object>();
        row2.put("xh", "2");
        row2.put("dw", "大同供电公司");
        row2.put("jxsb", "220千伏大同东变2号主变");
        row2.put("jxsj", "2024-04-21 09:00-17:00");
        row2.put("bz", "设备缺陷处理，电网风险等级：绿色");
        data1.add(row2);

        Map<String, Object> row3 = new HashMap<String, Object>();
        row3.put("xh", "3");
        row3.put("dw", "阳泉供电公司");
        row3.put("jxsb", "110千伏阳泉变3号主变");
        row3.put("jxsj", "2024-04-22 08:30-16:30");
        row3.put("bz", "预防性试验，电网风险等级：绿色");
        data1.add(row3);

        Map<String, Object> row4 = new HashMap<String, Object>();
        row4.put("xh", "4");
        row4.put("dw", "长治供电公司");
        row4.put("jxsb", "220千伏长治南变1号主变");
        row4.put("jxsj", "2024-04-23 07:00-19:00");
        row4.put("bz", "技术改造，电网风险等级：橙色");
        data1.add(row4);

        Map<String, Object> row5 = new HashMap<String, Object>();
        row5.put("xh", "5");
        row5.put("dw", "晋城供电公司");
        row5.put("jxsb", "500千伏晋城变2号主变");
        row5.put("jxsj", "2024-04-24 08:00-18:00");
        row5.put("bz", "年度检修，电网风险等级：黄色");
        data1.add(row5);

        dto1.put("data", data1);
        dto1.put("cols", cols1);
        result.add(dto1);

        // T14 - 二次作业风险表
        LinkedHashMap<String, Object> dto2 = new LinkedHashMap<String, Object>();
        String[] cols2 = {"xh", "dw", "ztxmmc", "zysj", "zyfxdj", "bz"};
        List<Map<String, Object>> data2 = new ArrayList<Map<String, Object>>();

        // 生成二次作业风险数据
        Map<String, Object> risk1 = new HashMap<String, Object>();
        risk1.put("xh", "1");
        risk1.put("dw", "太原供电公司");
        risk1.put("ztxmmc", "500千伏太原变保护定值修改");
        risk1.put("zysj", "2024-04-20 10:00-12:00");
        risk1.put("zyfxdj", "中风险");
        risk1.put("bz", "配合主变检修，作业风险等级：中等，已制定详细安全措施");
        data2.add(risk1);

        Map<String, Object> risk2 = new HashMap<String, Object>();
        risk2.put("xh", "2");
        risk2.put("dw", "大同供电公司");
        risk2.put("ztxmmc", "220千伏大同东变自动化调试");
        risk2.put("zysj", "2024-04-21 14:00-16:00");
        risk2.put("zyfxdj", "低风险");
        risk2.put("bz", "常规调试作业，作业风险等级：低等，按标准流程执行");
        data2.add(risk2);

        Map<String, Object> risk3 = new HashMap<String, Object>();
        risk3.put("xh", "3");
        risk3.put("dw", "长治供电公司");
        risk3.put("ztxmmc", "220千伏长治南变通信设备更换");
        risk3.put("zysj", "2024-04-23 09:00-15:00");
        risk3.put("zyfxdj", "高风险");
        risk3.put("bz", "涉及重要通信链路，作业风险等级：高等，需专人监护");
        data2.add(risk3);

        dto2.put("data", data2);
        dto2.put("cols", cols2);
        result.add(dto2);

        return result;
    }

    /**
     * 文本样式标记处理方法
     *
     * 功能说明：
     * 为不同类型的文本内容添加特殊的Unicode标记符号，用于后续样式渲染识别
     *
     * 处理规则：
     * 1. 普通填报项：使用"龘"标记，不带序号
     *    格式：龘 + 空格 + 文本内容
     * 2. 重点工作项：使用"骉"标记，带自动序号
     *    格式：骉 + 空格 + 序号 + 文本内容
     * 3. 多行文本：第一行带标记，后续行只保持缩进对齐
     *
     * @param result 原始文本数据映射
     * @return 处理后带样式标记的文本数据映射
     */
    public static Map<String, String> buildTextSplit(Map<String, String> result) {
        Map<String, String> rs = new HashMap<String, String>();
        Object[] keys = result.keySet().toArray();

        for (int i = 0; i < keys.length; i++) {
            String key = (String) keys[i];
            String value = result.get(key);

            if (value != null) {
                // 获取样式标记配置
                String[] vf = colxhs.get("vf");      // 样式标记符号：龘、骉、魑、魍
                String[] xhs = colxhs.get("xh");     // 序号列表：1. 2. 3. ...
                String[] text = colxhs.get("text");  // 需要带序号的字段列表

                // 按行分割文本内容
                String[] split = value.split("\r|\n");

                // 检查当前字段是否需要带序号（重点工作项）
                int index = -1;
                for (int j = 0; j < text.length; j++) {
                    if (text[j].equals(key)) {
                        index = j;  // 找到对应的序号索引
                        break;
                    }
                }

                // 根据是否需要序号，应用不同的标记格式
                if (index > -1) {
                    // 重点工作项：使用"骉"标记 + 序号
                    for (int j = 0; j < split.length; j++) {
                        if (j == 0) {
                            // 第一行：标记符 + 序号 + 内容
                            value = vf[1] + "       " + xhs[index] + split[0];
                        } else {
                            // 后续行：保持缩进对齐
                            value = value + "\n" + "       " + split[j];
                        }
                    }
                } else {
                    // 普通填报项：使用"龘"标记，不带序号
                    for (int j = 0; j < split.length; j++) {
                        if (j == 0) {
                            // 第一行：标记符 + 内容
                            value = vf[0] + "       " + split[0];
                        } else {
                            // 后续行：保持缩进对齐
                            value = value + "\n" + "       " + split[j];
                        }
                    }
                }
            }
            rs.put(key, value);
        }
        return rs;
    }

    /******************************************************************************************************************/

    // 第一步：构建文本
    private static void changeTxt(XMLSlideShow slideShow) {
        List<XSLFSlide> slidesList = slideShow.getSlides();
        for (XSLFSlide slide : slidesList) {
            List<XSLFShape> shapesList = slide.getShapes();
            for (XSLFShape shape : shapesList) {
                if (shape instanceof XSLFAutoShape) {
                    XSLFAutoShape autoShape = (XSLFAutoShape) shape;
                    String paragraphText = autoShape.getText();
                    XSLFTextParagraph n = null;
                    XSLFTextRun run = null;
                    // 段落对齐方式
                    Map<String, Object> center = drawRunAligns.get("center");
                    Map<String, Object> left = drawRunAligns.get("left");
                    // 构建样式：渲染样式
                    Map<String, Object> title = drawRunStyles.get("title");
                    Map<String, Object> vMonth = drawRunStyles.get("vMonth");
                    Map<String, Object> text = drawRunStyles.get("text");
                    // 当前段落 匹配 rows的Key
                    Object[] keys = rows.keySet().toArray();
                    for (int i = 0; i < rows.size(); i++) {
                        String key = (String) keys[i];
                        String value = rows.get(key);
                        if (paragraphText.contains("{" + key + "}")) {
                            if ("cs".equals(key) || "rq".equals(key)) {
                                drawRunPara(drawRunClear(autoShape, n, center), run, value, title);
                            } else if ("vMonth".equals(key)) {
                                drawRunPara(drawRunClear(autoShape, n, left), run, value, vMonth);
                            } else {
                                drawRunPara(drawRunClear(autoShape, n, left), run, value, text);
                            }
                        }
                    }
                }
            }
        }
    }

    /******************************************************************************************************************/

    // 第二步：构建表格
    private static void changeTab(XMLSlideShow slideShow, List<LinkedHashMap<String, Object>> list) {
        // 获取每个List数据
        // 说明：因为循环渲染，发现表格重叠，因未搞清楚重叠原因，故这里没有循环渲染表格
        List<Map<String, Object>> dt1 = (List<Map<String, Object>>) list.get(0).get("data");
        String[] cols1 = (String[]) list.get(0).get("cols");
        List<Map<String, Object>> dt2 = (List<Map<String, Object>>) list.get(1).get("data");
        String[] cols2 = (String[]) list.get(1).get("cols");

        //判别是第几张表格
        int fCount = 0;
        List<XSLFSlide> slidesList = slideShow.getSlides();
        for (XSLFSlide slide : slidesList) {
            List<XSLFShape> shapesList = slide.getShapes();
            for (XSLFShape shape : shapesList) {
                if (shape instanceof XSLFTable) {
                    XSLFTable table = (XSLFTable) shape;
                    if (fCount == 0) {
                        fCount++;
                        int totalRows = dt1.size();
                        int maxRowsPerPage = 8;
                        int currentPageRows = Math.min(totalRows,maxRowsPerPage);
                        // 获取表格所在页的高度
//                        double totalHeight = slide.getSlideShow().getPageSize().getHeight();
                        // 设置表格所在页的高度
                        double totalHeight = 400.0;
                        double rowHeigt = totalHeight / maxRowsPerPage;
                        for (int rowIndex = 0;rowIndex<currentPageRows;rowIndex++) {
                            Map<String, Object> rowData = dt1.get(rowIndex);
                            XSLFTableRow tableRow = table.addRow();
                            tableRow.setHeight(rowHeigt);
                            for (int j = 0; j < cols1.length; j++) {
                                XSLFTableCell cell = tableRow.addCell();
                                XSLFTextParagraph paragraph = cell.addNewTextParagraph();
                                //设置文本居中
                                paragraph.setTextAlign(TextAlign.CENTER);
                                paragraph.setBulletFont("微软雅黑");
                                paragraph.setBullet(false);
                                XSLFTextRun run = paragraph.addNewTextRun();
                                run.setText(FactoryUtils.objToStr(rowData.get(cols1[j])));
                                run.setFontSize(12.0D);
                                tableStyle(cell);
                            }
                        }
                    } else if (fCount == 1) {
                        fCount++;
                        int totalRows = dt2.size();
                        int maxRowsPerPage = 10;
                        int currentPageRows = Math.min(totalRows,maxRowsPerPage);
                        //设置表格所在页的高度
                        double totalHeight = 400.0;
                        double rowHeigt = totalHeight / maxRowsPerPage;
                        for (int rowIndex = 0;rowIndex<currentPageRows;rowIndex++) {
                            Map<String, Object> rowData = dt2.get(rowIndex);
                            XSLFTableRow tableRow = table.addRow();
                            tableRow.setHeight(rowHeigt);
                            for (int j = 0; j < cols2.length; j++) {
                                XSLFTableCell cell = tableRow.addCell();
                                XSLFTextParagraph paragraph = cell.addNewTextParagraph();
                                //设置文本居中
                                paragraph.setTextAlign(TextAlign.CENTER);
                                paragraph.setBulletFont("微软雅黑");
                                paragraph.setBullet(false);
                                XSLFTextRun run = paragraph.addNewTextRun();
                                run.setText(FactoryUtils.objToStr(rowData.get(cols2[j])));
                                run.setFontSize(12.0D);
                                tableStyle(cell);
                            }
                        }
                    }
                }
            }
        }
    }

    // 表格添加框线及框线颜色
    public static void tableStyle(XSLFTableCell cell) {
        cell.setBorderColor(BorderEdge.bottom, Color.black);
        cell.setBorderColor(BorderEdge.left, Color.black);
        cell.setBorderColor(BorderEdge.top, Color.black);
        cell.setBorderColor(BorderEdge.right, Color.black);
        cell.setBorderWidth(BorderEdge.bottom, 1.0);
        cell.setBorderWidth(BorderEdge.left, 1.0);
        cell.setBorderWidth(BorderEdge.top, 1.0);
        cell.setBorderWidth(BorderEdge.right, 1.0);
        cell.setFillColor(Color.white);
    }

    /******************************************************************************************************************/

    // 第三步：构建图片
    private static void changePic(XMLSlideShow slideShow, String rq) {
        try {
            List<XSLFSlide> slidesList = slideShow.getSlides();
            for (XSLFSlide slide : slidesList) {
                List<XSLFShape> shapesList = slide.getShapes();

                // 收集需要替换的图片信息，避免在遍历时修改集合
                List<PictureReplaceInfo> replaceInfos = new ArrayList<>();

                // 第一步：收集所有需要替换的图片信息
                for (int i = 0; i < shapesList.size(); i++) {
                    XSLFShape shape = shapesList.get(i);
                    if (shape instanceof XSLFPictureShape) {
                        Rectangle2D anchor = shape.getAnchor();
                        File imageFile = new File(imgPath + "module2" + FILE_SUFFIX);
                        if (imageFile.exists()) {
                            PictureReplaceInfo info = new PictureReplaceInfo();
                            info.originalShape = (XSLFPictureShape) shape;
                            info.anchor = anchor;
                            info.imageFile = imageFile;
                            replaceInfos.add(info);
                        }
                    }
                }

                // 第二步：执行图片替换
                for (PictureReplaceInfo info : replaceInfos) {
                    FileInputStream fis = new FileInputStream(info.imageFile);
                    byte[] newImageBytes = new byte[(int) info.imageFile.length()];
                    fis.read(newImageBytes);
                    fis.close();

                    // addPicture方法返回XSLFPictureData对象
                    XSLFPictureData pictureData = slideShow.addPicture(newImageBytes, PictureData.PictureType.PNG);
                    XSLFPictureShape newPic = slide.createPicture(pictureData);
                    newPic.setAnchor(info.anchor);

                    // 删除原来的图片（可选，避免重叠）
                    slide.removeShape(info.originalShape);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // 图片替换信息类
    private static class PictureReplaceInfo {
        XSLFPictureShape originalShape;
        Rectangle2D anchor;
        File imageFile;
    }

    /******************************************************************************************************************/

    // 第四步：构建样式
    private static void buildStyle(XMLSlideShow slideShow) {
        // 遍历文档中的每个段落
        List<XSLFSlide> slidesList = slideShow.getSlides();
        for (XSLFSlide slide : slidesList) {
            List<XSLFShape> shapesList = slide.getShapes();
            for (XSLFShape shape : shapesList) {
                if (shape instanceof XSLFAutoShape) {
                    XSLFAutoShape autoShape = (XSLFAutoShape) shape;
                    String paragraphText = autoShape.getText();
                    XSLFTextParagraph n = null;
                    XSLFTextRun run = null;

                    // 判断样式类型
                    int vflag = isType(paragraphText);
//                    print(paragraphText);
//                    print(vflag);
//                    print("");

                    // 处理各种样式
                    if (vflag > -1) {
                        // 将"龘", "骉", "魑", "魍"标志文字删除
                        paragraphText = paragraphText.substring(1);
                        // 循环当前段落中的全部关键字，将索引存储起来段落
                        List<int[]> pos = findBoldIndex(paragraphText, vflag);
                        if (pos.size() > 0) {
                            // 合并重复索引
                            List<int[]> mergedRanges = mergeRanges(pos);
                            // 合并段落：有索引
                            mergeParas(autoShape, n, run, mergedRanges, paragraphText);
                        } else {
                            // 合并段落：无索引
                            mergeParas(autoShape, n, run, paragraphText, vflag);
                        }
                    }
                }
            }
        }
    }

    // 判断样式类型
    public static int isType(String paragraphText) {
        String[] vf = colxhs.get("vf");
        int index = -1;
        for (int j = 0; j < vf.length; j++) {
            if (!"".equals(paragraphText)) {
                if (vf[j].equals(paragraphText.substring(0, 1))) {
                    index = j;
                    break;
                }
            }
        }
        return index;
    }

    // 循环当前段落中的全部关键字，将索引存储起来段落
    public static List<int[]> findBoldIndex(String paragraphText, int vflag) {
        List<int[]> pos = new ArrayList<int[]>();
        // 第一个句号前的文字加粗
        // 骉：填报项-重点工作，带序号
        if (vflag == 1) {
            int index = paragraphText.indexOf("。");
            if (index > 0) {
                pos.add(new int[]{0, index});
            }
        }
        // 关键字加粗
        for (String keyword : highlights.get("keywords")) {
            if (paragraphText.contains(keyword)) {
                int index = paragraphText.indexOf(keyword);
                while (index >= 0) {
                    pos.add(new int[]{index, index + keyword.length() - 1});
                    index = paragraphText.indexOf(keyword, index + 1);
                }
            }
        }
        return pos;
    }

    // 合并重复索引
    public static List<int[]> mergeRanges(List<int[]> pos) {
        // 如果范围为空，直接返回空列表
        if (pos.isEmpty()) {
            return new ArrayList<int[]>();
        }
        // 对pos按照第一个元素的大小进行排序
        Collections.sort(pos, new Comparator<int[]>() {
            public int compare(int[] a, int[] b) {
                if (a[0] < b[0]) {
                    return -1;
                } else if (a[0] > b[0]) {
                    return 1;
                } else {
                    return 0;
                }
            }
        });
        List<int[]> mergedRanges = new ArrayList<int[]>();
        int[] currentRange = pos.get(0);
        // 遍历范围列表并合并相邻的范围
        for (int i = 1; i < pos.size(); i++) {
            int[] nextRange = pos.get(i);
            if (currentRange[1] >= nextRange[0]) {
                // 合并范围
                currentRange[1] = Math.max(currentRange[1], nextRange[1]);
            } else {
                // 将当前范围添加到合并后的范围列表中，并更新当前范围为下一个范围
                mergedRanges.add(currentRange);
                currentRange = nextRange;
            }
        }
        // 将最后一个范围添加到合并后的范围列表中
        mergedRanges.add(currentRange);
        return mergedRanges;
    }

    // 合并段落：有索引
    public static void mergeParas(XSLFAutoShape autoShape, XSLFTextParagraph n, XSLFTextRun run, List<int[]> mergedRanges, String paragraphText) {
        // 段落对齐方式
        Map<String, Object> left = drawRunAligns.get("left");
        // 构建样式：渲染样式
        Map<String, Object> normal = drawRunStyles.get("normal");
        Map<String, Object> high = drawRunStyles.get("high");

        // 清空runs
        n = drawRunClear(autoShape, n, left);

        // 是否索引大小为1，如果为1，代表样式只修改一处；如果大于1，代表样式修改多处
        if (mergedRanges.size() == 1) {
            // 样式只修改一处
            // 初始参数
            int[] range = mergedRanges.get(0);
            int startIndex = range[0];
            int endIndex = range[1];

            // 拼接段落
            // ppt：paragraphText无换行符
            // word：paragraphText.length()-1 去除换行符，paragraphText.length() 保留换行符
            String beforeRun = paragraphText.substring(0, startIndex);
            String keywordRun = paragraphText.substring(startIndex, endIndex + 1);
            String afterRun = paragraphText.substring(endIndex + 1, paragraphText.length());
            drawRunPara(n, run, beforeRun, normal);
            drawRunPara(n, run, keywordRun, high);
            drawRunPara(n, run, afterRun, normal);
        } else {
            // 样式修改多处
            for (int i = 0; i < mergedRanges.size(); i++) {
                // 初始参数
                int[] range = mergedRanges.get(i);
                int[] range2 = i == mergedRanges.size() - 1 ? mergedRanges.get(i) : mergedRanges.get(i + 1);
                int startIndex = range[0];
                int endIndex = range[1];
                int beforeIndex = range2[0];

                // 拼接段落
                // ppt：paragraphText无换行符
                // word：paragraphText.length()-1 去除换行符，paragraphText.length() 保留换行符
                if (i == 0) {
                    String beforeRun = paragraphText.substring(0, startIndex);
                    String keywordRun = paragraphText.substring(startIndex, endIndex + 1);
                    String afterRun = paragraphText.substring(endIndex + 1, beforeIndex);
                    drawRunPara(n, run, beforeRun, normal);
                    drawRunPara(n, run, keywordRun, high);
                    drawRunPara(n, run, afterRun, normal);
                } else if (i == mergedRanges.size() - 1) {
                    String keywordRun = paragraphText.substring(startIndex, endIndex + 1);
                    String afterRun = paragraphText.substring(endIndex + 1, paragraphText.length());
                    drawRunPara(n, run, keywordRun, high);
                    drawRunPara(n, run, afterRun, normal);
                } else {
                    String keywordRun = paragraphText.substring(startIndex, endIndex + 1);
                    String afterRun = paragraphText.substring(endIndex + 1, beforeIndex);
                    drawRunPara(n, run, keywordRun, high);
                    drawRunPara(n, run, afterRun, normal);
                }
            }
        }
    }

    // 合并段落：无索引
    public static void mergeParas(XSLFAutoShape autoShape, XSLFTextParagraph n, XSLFTextRun run, String paragraphText, int vflag) {
        // 段落对齐方式
        Map<String, Object> left = drawRunAligns.get("left");
        // 构建样式：渲染样式
        Map<String, Object> style = drawRunStyles.get("normal");
        if (vflag == 0) {
            style = drawRunStyles.get("normal");
        } else if (vflag == 1) {
            style = drawRunStyles.get("high");
        }

        // 清空runs
        n = drawRunClear(autoShape, n, left);
        String text = paragraphText;
        drawRunPara(n, run, text, style);
    }

    // 设置对齐，清空runs
    public static XSLFTextParagraph drawRunClear(XSLFAutoShape autoShape, XSLFTextParagraph n, Map<String, Object> map) {
        autoShape.clearText();
        n = autoShape.addNewTextParagraph();
        n.setBulletFont((String) map.get("bulletfont"));
        n.setTextAlign((TextAlign) map.get("textalign"));
        n.setBullet((Boolean) map.get("bullet"));
        return n;
    }

    // 绘制段落，拼接段落
    public static void drawRunPara(XSLFTextParagraph n, XSLFTextRun run, String value, Map<String, Object> map) {
        run = n.addNewTextRun();
        run.setText(value);
        run.setFontFamily((String) map.get("fontfamily"));
        run.setFontSize((Double) map.get("fontsize"));
        run.setBold((Boolean) map.get("bold"));
        run.setFontColor((Color) map.get("fontcolor"));
    }
}
