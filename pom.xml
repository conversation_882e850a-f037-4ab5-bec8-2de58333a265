<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <!--
        spring-boot-starter-parent 是 Spring Boot 提供的父 POM，主要作用：
        1. 统一管理项目的依赖版本和插件版本，减少手动配置的复杂性。
        2. 提供合理的 Maven 插件默认配置，如编译、打包和测试插件。
        3. 推荐用于 Spring Boot 项目的 Maven 构建中，简化配置。
        版本 2.6.6 对应 Spring Boot 2.6.6 版本。
        -->
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.6.6</version>
    </parent>

    <!--gav-->
    <groupId>cn.jggroup</groupId>
    <artifactId>ydsz-boot-device</artifactId>
    <version>1.0.0</version>

    <name>ydsz-boot-example</name>
    <packaging>jar</packaging>

    <!-- 所有子模块共同的依赖 -->
    <dependencies>
        <!--=======================================注册服务：主系统=========================================-->
        <!--
        ydsz-module-system 是核心业务模块，已包括【dependencyManagement】定义的全部依赖，如需引入，请避免重复与版本冲突
        主要功能：
        1. 提供基础业务功能：用户、角色、权限、菜单、部门等管理
        2. 集成多种存储方案：本地、七牛云、MinIO、阿里云OSS
        3. 提供安全认证体系：JWT令牌、Shiro权限框架
        4. 支持服务监控：Redis、SQL、服务器状态监控
        5. 包含缓存、数据源、线程池等系统级功能
        6. 集成积木报表功能：支持自定义报表模板和数据源配置
        -->
        <dependency>
            <groupId>cn.jggroup</groupId>
            <artifactId>ydsz-module-system-depend</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <classifier>original</classifier>
            <systemPath>${project.basedir}/lib/ydsz-module-system-depend/ydsz-module-system-depend-1.0.0.jar</systemPath>
        </dependency>

        <!--=======================================开源服务utils：用于poi======================================-->
        <!--poi
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>4.1.2</version>
        </dependency>-->

        <!--=======================================开源服务utils：用于aspose======================================-->
        <!--aspose-cells-->
        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-cells</artifactId>
            <version>21.8</version>
            <scope>system</scope>
            <classifier>original</classifier>
            <systemPath>${project.basedir}/lib/ydsz-module-aspose/aspose-cells-21.8.jar</systemPath>
        </dependency>
        <!--aspose-slides-->
        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-slides</artifactId>
            <version>21.8</version>
            <scope>system</scope>
            <classifier>original</classifier>
            <systemPath>${project.basedir}/lib/ydsz-module-aspose/aspose-slides-21.8-jdk16-crack.jar</systemPath>
        </dependency>
        <!--aspose-words-->
        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-words</artifactId>
            <version>14.12.0</version>
            <scope>system</scope>
            <classifier>original</classifier>
            <systemPath>${project.basedir}/lib/ydsz-module-aspose/aspose-words-14.12.0-jdk16.jar</systemPath>
        </dependency>
    </dependencies>

    <!-- 版本仲裁中心：父pom只声明版本，子pom需要显示声明才能启用-->
    <dependencyManagement>
        <dependencies>
            <!--=======================================springboot系列1=========================================-->
            <!--
            spring-boot-starter 是 Spring Boot 的核心依赖包，包含以下模块：
            1. Spring Framework 的基础模块（如 spring-beans、spring-context）。
            2. 日志功能（默认使用 SLF4J 和 Logback）。
            3. 提供 Spring Boot 应用运行所需的最小依赖集合。
            -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>2.6.6</version>
            </dependency>
            <!-- Spring Boot Web: 提供 Web 应用开发的核心依赖，包括 MVC 和嵌入式服务器 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>2.6.6</version>
            </dependency>
            <!-- Spring Boot Test: 包含测试工具和框架，如 JUnit、Mockito，默认包括 slf4j -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>2.6.6</version>
            </dependency>
            <!-- Spring Boot Redis: 提供 Redis 数据访问支持 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>2.6.6</version>
            </dependency>

            <!--=======================================springboot系列2=========================================-->
            <!-- Spring Boot WebSocket: 支持 WebSocket 通信 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>2.6.6</version>
            </dependency>
            <!-- Spring Boot Mail: 提供邮件发送支持 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-mail</artifactId>
                <version>2.6.6</version>
            </dependency>
            <!-- Spring Boot AOP: 支持面向切面编程（Aspect Oriented Programming） -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>2.6.6</version>
            </dependency>
            <!-- Spring Boot Validation: 支持数据校验（如 @Valid 和 @Validated 注解） -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>2.6.6</version>
            </dependency>
            <!-- Spring Boot Freemarker: 提供 FreeMarker 模板引擎支持 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-freemarker</artifactId>
                <version>2.6.6</version>
            </dependency>
            <!-- Spring Boot Actuator: 提供应用监控和指标采集 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>2.6.6</version>
            </dependency>
            <!-- Micrometer Prometheus: 提供 Prometheus 的指标采集支持 -->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>1.8.4</version>
            </dependency>
            <!-- Spring Boot Quartz: 提供定时任务管理支持 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-quartz</artifactId>
                <version>2.6.6</version>
            </dependency>
            <!-- Knife4j: 增强版 Swagger 文档生成工具，提供更友好的 API 文档界面 -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>3.0.3</version>
            </dependency>
            <!-- freemarker: 一个用于生成动态网页或文件的模板引擎 -->
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>2.3.30</version>
            </dependency>

            <!--=======================================开源服务utils：基础工具类==================================-->
            <!-- commons-lang: 提供对 Java 基础类库（如 String、Number）的早期增强工具类 -->
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <!-- commons-lang3: 提供对 Java 基础类库（如 String、Object）的增强工具类，是 commons-lang 的升级版 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.10</version>
            </dependency>
            <!-- commons-io: 提供文件、流、路径等 IO 操作的工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.15.1</version>
            </dependency>
            <!-- commons-text: 提供字符串处理相关的高级工具类 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.11.0</version>
            </dependency>
            <!-- commons-pool2: 提供对象池管理功能的工具类库，用于资源复用 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.11.1</version>
            </dependency>
            <!-- commons-codec: 提供常见的编码解码功能，例如 Base64 编码/解码、URL 编码/解码、哈希算法等 -->
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.14</version>
            </dependency>
            <!-- Hutool: 一个国产的 Java 工具类库，集成了常用工具方法（如日期、字符串、文件操作等） -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.5.4</version>
            </dependency>
            <!-- Guava: Google 提供的核心工具库，包含集合、缓存、并发、字符串操作等功能 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>29.0-jre</version>
            </dependency>

            <!--=======================================开源服务utils：处理各种类型================================-->
            <!-- OkHttp: 一个轻量级、高性能的 HTTP 客户端库，用于发送 HTTP 请求 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.4.1</version>
            </dependency>
            <!-- dom4j: 一个用于解析和操作 XML 文档的 Java 库，支持 XPath 查询 -->
            <dependency>
                <groupId>dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>1.6.1</version>
            </dependency>
            <!-- Lombok: 提供简化 Java 代码的注解，如自动生成 Getter/Setter、构造方法等 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.22</version>
            </dependency>
            <!-- FastJSON: 一个高性能的 JSON 解析和序列化工具，支持 JSON 转对象和对象转 JSON -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <!-- Pegdown: 一个轻量级的 Markdown 解析器，用于将 Markdown 转换为 HTML -->
            <dependency>
                <groupId>org.pegdown</groupId>
                <artifactId>pegdown</artifactId>
                <version>1.6.0</version>
            </dependency>
            <!-- Pinyin4j: 一个将汉字转换为拼音的工具库，用于处理中文拼音相关功能 -->
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>2.5.0</version>
            </dependency>
            <!-- commons-beanutils: 提供对 Java Bean 属性的操作工具类（如拷贝、转换等功能） -->
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.9.4</version>
            </dependency>
            <!-- gson: Google 提供的一个 Java 库，用于将 Java 对象转换为 JSON 字符串，反之亦然 -->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.8.6</version>
            </dependency>
            <!-- json-lib: 用于在 Java 中处理 JSON 数据的库，支持将 Java 对象与 JSON 格式互转，支持多种 JSON 格式 -->
            <dependency>
                <groupId>net.sf.json-lib</groupId>
                <artifactId>json-lib</artifactId>
                <version>2.4</version>
                <classifier>jdk15</classifier>
            </dependency>
            <!-- jdom: 一个用于处理 XML 的 Java 库，提供了比传统的 DOM 更简洁、更易用的 API，支持创建、解析和修改 XML 文档 -->
            <dependency>
                <groupId>org.jdom</groupId>
                <artifactId>jdom</artifactId>
                <version>1.1</version>
            </dependency>
            <!-- 二维码: ZXing (Zebra Crossing) 是一个开源的二维码生成与解码库，用于生成二维码或扫描二维码 -->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>3.3.1</version>
            </dependency>
            <!-- 阿里SDK: 阿里巴巴提供的 Java SDK，用于集成支付宝的支付功能，包括支付请求、退款等操作 -->
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>3.1.0</version>
            </dependency>

            <!--=======================================开源服务：连接sql========================================-->
            <!--mybatis-plus-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.5.1</version>
            </dependency>
            <!--p6spy-->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>3.9.1</version>
            </dependency>
            <!--druid-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>1.2.18</version>
            </dependency>
            <!--dynamic-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>3.2.0</version>
            </dependency>
            <!--mysql-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.27</version>
                <scope>runtime</scope>
            </dependency>
            <!--sqlserver-->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>sqljdbc4</artifactId>
                <version>4.0</version>
                <scope>runtime</scope>
            </dependency>
            <!--oracle-->
            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc6</artifactId>
                <version>11.2.0.3</version>
                <scope>runtime</scope>
            </dependency>
            <!--postgresql-->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>42.2.25</version>
                <scope>runtime</scope>
            </dependency>

            <!--=======================================开源服务：上传文件========================================-->
            <!-- commons-fileupload: 用于处理文件上传功能 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.4</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 七牛云 SDK: 用于调用七牛云存储服务进行文件上传和管理 -->
            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>7.4.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>okhttp</artifactId>
                        <groupId>com.squareup.okhttp3</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- MinIO SDK: 用于调用 MinIO 对象存储服务，进行文件上传与管理 -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>8.0.3</version>
                <exclusions>
                    <exclusion>
                        <artifactId>okio</artifactId>
                        <groupId>com.squareup.okio</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>okhttp</artifactId>
                        <groupId>com.squareup.okhttp3</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 阿里云短信: 用于调用阿里云短信服务 API，发送短信 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
                <version>2.1.0</version>
            </dependency>
            <!-- 阿里云 OSS: 用于调用阿里云对象存储服务（OSS）API，进行文件存储与管理 -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.11.2</version>
            </dependency>

            <!--=======================================开源服务：rbac权限=======================================-->
            <!-- JWT: 用于生成和验证 JSON Web Token 的工具 -->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>3.11.0</version>
            </dependency>
            <!-- Shiro: Apache Shiro 的 Spring Boot 集成，提供强大的身份认证与权限管理功能 -->
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-spring-boot-starter</artifactId>
                <version>1.12.0</version>
            </dependency>
            <!-- Shiro-Redis: 将 Apache Shiro 会话存储到 Redis 中的扩展 -->
            <dependency>
                <groupId>org.crazycake</groupId>
                <artifactId>shiro-redis</artifactId>
                <version>3.1.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.shiro</groupId>
                        <artifactId>shiro-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- JustAuth: 一个支持多平台的第三方登录工具 -->
            <dependency>
                <groupId>com.xkcoding.justauth</groupId>
                <artifactId>justauth-spring-boot-starter</artifactId>
                <version>1.3.4</version>
                <exclusions>
                    <exclusion>
                        <artifactId>hutool-core</artifactId>
                        <groupId>cn.hutool</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- Jackson Kotlin Module: 解决 OkHttp 引入 Kotlin 导致的启动警告问题 -->
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-kotlin</artifactId>
                <version>2.13.2</version>
            </dependency>

            <!--=======================================开源服务utils：用于编译poi======================================-->
            <!--mvc -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>5.3.18</version>
                <optional>true</optional>
            </dependency>
            <!--servlet -->
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>2.5</version>
                <scope>provided</scope>
                <optional>true</optional>
            </dependency>
            <!--poi-->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>4.1.2</version>
            </dependency>
            <!--sax-->
            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>2.9.1</version>
                <optional>true</optional>
            </dependency>
            <!-- slf4j -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.30</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>1.7.30</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>2.11.2</version>
                <optional>true</optional>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <!-- 指定构建的最终文件名，使用项目版本号 -->
        <finalName>${project.artifactId}</finalName>
        <!-- 资源文件配置 -->
        <resources>
            <!-- 1.Java目录下的资源文件 -->
            <resource>
                <directory>src/main/java</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.json</include>
                    <include>**/*.ftl</include>
                </includes>
            </resource>
            <!-- 2.resources目录下的资源文件 -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <!-- spring-boot-maven-plugin：可执行jar/war，如xxx-module-xxx-1.0.0-exec.jar-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.6.6</version>
                <configuration>
                    <!--允许本地的lib的jar包-->
                    <includeSystemScope>true</includeSystemScope>
                    <!-- 指定主类 -->
                    <mainClass>cn.jggroup.device.YdszDeviceApplication</mainClass>
                    <!-- 指定打包方式为 JAR -->
                    <layout>JAR</layout>
                    <!-- 生成的 jar 可直接执行 -->
                    <executable>true</executable>
                    <!-- 排除开发工具依赖 -->
                    <excludeDevtools>true</excludeDevtools>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <!-- 重新打包，将依赖打入 jar 包 -->
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- maven-compiler-plugin：可以被依赖的jar包，如xxx-module-xxx-1.0.0.jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <!-- maven-source-plugin：用于生成源码的jar包，如xxx-module-xxx-1.0.0-sources.jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <!-- 创建源码 jar -->
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- maven-surefire-plugin：打包跳过测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <!-- maven-resources-plugin：资源文件配置 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <useDefaultDelimiters>true</useDefaultDelimiters>
                    <!-- 字体文件不过滤 -->
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                        <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>doc</nonFilteredFileExtension>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ppt</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pptx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <!-- exec-maven-plugin：启动代码生成器，执行命令(mvn exec:exec) 或 双击IDEA按钮(IDEA -> Maven -> 插件 -> exec -> exex:exec)-->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <executable>cmd</executable>
                    <workingDirectory>${project.basedir}/lib/ydsz-module-code-standalone</workingDirectory>
                    <arguments>
                        <argument>/c</argument>
                        <argument>ydsz-module-code-standalone.bat</argument>
                    </arguments>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.4</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <!-- 发布版本构件的仓库 -->
        <repository>
            <id>rdc-releases</id>
            <url>https://packages.aliyun.com/maven/repository/2277291-release-HoKXzy/</url>
        </repository>
        <!-- 发布快照构件的仓库 -->
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <url>https://packages.aliyun.com/maven/repository/2277291-snapshot-HWJRaP/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <!-- 阿里云公共仓库 -->
        <repository>
            <id>aliyun</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <!-- 阿里云Spring仓库 -->
        <repository>
            <id>spring-aliyun</id>
            <name>Spring Aliyun</name>
            <url>https://maven.aliyun.com/repository/spring</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <!-- Maven中央仓库 -->
        <repository>
            <id>central</id>
            <name>Maven Central</name>
            <url>https://repo1.maven.org/maven2/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <!-- Spring官方仓库 -->
        <repository>
            <id>spring-releases</id>
            <name>Spring Releases</name>
            <url>https://repo.spring.io/release</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <!-- Apache快照仓库 -->
        <repository>
            <id>apache-snapshots</id>
            <name>Apache Snapshots</name>
            <url>https://repository.apache.org/snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <!-- 插件仓库配置 -->
    <pluginRepositories>
        <pluginRepository>
            <id>aliyun-plugin</id>
            <name>aliyun plugin</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>
